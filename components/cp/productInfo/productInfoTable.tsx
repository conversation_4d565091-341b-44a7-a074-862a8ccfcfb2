import React, { useState } from "react";
import {
  Button,
  Checkbox,
  Input,
  Modal,
  ModalContent,
  NumberInput,
  Select,
  SelectItem,
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
  Tooltip,
  useDisclosure,
} from "@heroui/react";
import { <PERSON>dal<PERSON>ody, ModalFooter, ModalHeader } from "@heroui/modal";

import { repayMethods } from "@/types";
import { DeleteIcon } from "@/components/icons";
import { ProductInfo } from "@/app/cp/creditInfoQuery";

const columns = [
  { name: "年利率", uid: "apr" },
  { name: "日利率", uid: "dayRate" },
  { name: "临价年利率", uid: "tempApr" },
  { name: "临价日利率", uid: "tempDayRate" },
  { name: "临价截止时间", uid: "tempPriceDueTime" },
  { name: "还款方式", uid: "repayMethod" },
  { name: "是否可提前还款", uid: "earlyRepay" },
  { name: "期数列表", uid: "termNums" },
  { name: "操作", uid: "actions" },
];

export default function ProductInfoTable(props: {
  productInfos?: ProductInfo[];
  setProductInfos: (productInfos: ProductInfo[]) => void;
}) {
  const [newProductInfo, setNewProductInfo] = useState<ProductInfo>({
    earlyRepay: false,
    termNums: [3, 6, 12],
  });
  const { isOpen, onOpen, onClose, onOpenChange } = useDisclosure();
  const createProductInfo = () => {
    props.setProductInfos([...props.productInfos, newProductInfo]);
    onClose();
  };

  function deleteProductInfo(repayMethod: number) {
    props.setProductInfos(
      props.productInfos.filter((item) => item.repayMethod !== repayMethod),
    );
  }

  const renderCell = React.useCallback((item: any, columnKey: any) => {
    const cellValue = item[columnKey];

    switch (columnKey) {
      case "earlyRepay":
        return <div>{cellValue ? "是" : "否"}</div>;
      case "termNums":
        return <div>{cellValue.join(",")}</div>;
      case "repayMethod":
        return (
          <div>
            {repayMethods.find((method) => method.key === cellValue)?.label ||
              ""}
          </div>
        );
      case "tempApr":
        return (
          <NumberInput
            className="max-w-xs"
            defaultValue={cellValue}
            label="临价年利率"
            onValueChange={(value) => {
              if (!value) {
                item[columnKey] = null;
              } else {
                item[columnKey] = value.toString();
              }
            }}
          />
        );
      case "actions":
        return (
          <div className="relative flex items-center gap-2">
            <Tooltip color="danger" content="删除">
              <span
                className="text-lg text-danger cursor-pointer active:opacity-50"
                onClick={() => deleteProductInfo(item.repayMethod)}
              >
                <DeleteIcon />
              </span>
            </Tooltip>
          </div>
        );
      default:
        return cellValue;
    }
  }, []);

  const topContent = (
    <div className="flex justify-between items-center">
      <div>还款方式</div>
      <Button color="primary" size="sm" onPress={onOpen}>
        新增
      </Button>
    </div>
  );

  return (
    <>
      <Table className={"col-span-full"} topContent={topContent}>
        <TableHeader columns={columns}>
          {(column) => (
            <TableColumn
              key={column.uid}
              align={column.uid === "actions" ? "center" : "start"}
            >
              {column.name}
            </TableColumn>
          )}
        </TableHeader>
        <TableBody items={props.productInfos}>
          {(item) => (
            <TableRow key={item.repayMethod}>
              {(columnKey) => (
                <TableCell>{renderCell(item, columnKey)}</TableCell>
              )}
            </TableRow>
          )}
        </TableBody>
      </Table>
      <Modal isOpen={isOpen} placement="top-center" onOpenChange={onOpenChange}>
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="flex flex-col gap-1">
                创建新的还款方式
              </ModalHeader>
              <ModalBody>
                <Input
                  label="年利率"
                  variant="bordered"
                  onValueChange={(value) =>
                    setNewProductInfo({ ...newProductInfo, apr: value })
                  }
                />
                <Input
                  label="日利率"
                  variant="bordered"
                  onValueChange={(value) =>
                    setNewProductInfo({ ...newProductInfo, dayRate: value })
                  }
                />
                <Input
                  label="临价年利率"
                  variant="bordered"
                  onValueChange={(value) =>
                    setNewProductInfo({ ...newProductInfo, tempApr: value })
                  }
                />
                <Input
                  label="临价日利率"
                  variant="bordered"
                  onValueChange={(value) =>
                    setNewProductInfo({ ...newProductInfo, tempDayRate: value })
                  }
                />

                <Select
                  className="max-w-xs"
                  label="还款方式"
                  onSelectionChange={(key) => {
                    setNewProductInfo({
                      ...newProductInfo,
                      repayMethod: parseInt(key.anchorKey as string),
                      repayMethodName:
                        repayMethods.find(
                          (method) => method.key === key.anchorKey,
                        )?.label || "",
                    });
                  }}
                >
                  {repayMethods.map((status) => (
                    <SelectItem key={status.key}>{status.label}</SelectItem>
                  ))}
                </Select>
                <Checkbox
                  isSelected={newProductInfo.earlyRepay}
                  onChange={() =>
                    setNewProductInfo({
                      ...newProductInfo,
                      earlyRepay: !newProductInfo.earlyRepay,
                    })
                  }
                >
                  是否提前还款
                </Checkbox>
                <div>还款期数</div>
                <div className="flex gap-4">
                  <Checkbox
                    isSelected={newProductInfo.termNums.includes(3)}
                    radius="md"
                    onValueChange={(isSelected) => {
                      if (isSelected) {
                        setNewProductInfo({
                          ...newProductInfo,
                          termNums: [...newProductInfo.termNums, 3],
                        });
                      } else {
                        setNewProductInfo({
                          ...newProductInfo,
                          termNums: newProductInfo.termNums.filter(
                            (num) => num !== 3,
                          ),
                        });
                      }
                    }}
                  >
                    3期
                  </Checkbox>
                  <Checkbox
                    defaultSelected
                    isSelected={newProductInfo.termNums.includes(6)}
                    radius="md"
                    onValueChange={(isSelected) => {
                      if (isSelected) {
                        setNewProductInfo({
                          ...newProductInfo,
                          termNums: [...newProductInfo.termNums, 6],
                        });
                      } else {
                        setNewProductInfo({
                          ...newProductInfo,
                          termNums: newProductInfo.termNums.filter(
                            (num) => num !== 6,
                          ),
                        });
                      }
                    }}
                  >
                    6期
                  </Checkbox>
                  <Checkbox
                    defaultSelected
                    isSelected={newProductInfo.termNums.includes(12)}
                    radius="md"
                    onValueChange={(isSelected) => {
                      if (isSelected) {
                        setNewProductInfo({
                          ...newProductInfo,
                          termNums: [...newProductInfo.termNums, 12],
                        });
                      } else {
                        setNewProductInfo({
                          ...newProductInfo,
                          termNums: newProductInfo.termNums.filter(
                            (num) => num !== 12,
                          ),
                        });
                      }
                    }}
                  >
                    12期
                  </Checkbox>
                </div>
              </ModalBody>
              <ModalFooter>
                <Button color="danger" variant="flat" onPress={onClose}>
                  关闭
                </Button>
                <Button color="primary" onPress={createProductInfo}>
                  创建
                </Button>
              </ModalFooter>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
}
