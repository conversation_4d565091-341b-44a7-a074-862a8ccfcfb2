import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow,
} from "@heroui/react";
import { BankCard } from "@/types/cp/bankCard";

const columns = [
  { name: "绑卡Id", uid: "bankCardId" },
  { name: "银行卡号", uid: "bankCardNo" },
  { name: "银行名称", uid: "bankName" },
  { name: "银行代码", uid: "bankCode" },
  { name: "卡类型", uid: "cardType" },
  { name: "单日限额", uid: "dayLimit" },
  { name: "单笔限额", uid: "singleLimit" },
];

export default function BankCardListTable(props: {
  bankCardList?: BankCard[];
}) {
  const renderCell = React.useCallback((item: any, columnKey: any) => {
    switch (columnKey) {
      case "cardType":
        return item[columnKey] === 1 ? "借记卡" : item[columnKey] === 2 ? "信用卡" : "";
      default:
        return item[columnKey];
    }
  }, []);

  const topContent = (
    <div className="flex justify-between items-center">
      <div>当前绑定的银行卡</div>
    </div>
  );

  return (
    <>
      <Table className={props.className} topContent={topContent}>
        <TableHeader columns={columns}>
          {(column) => (
            <TableColumn
              key={column.uid}
            >
              {column.name}
            </TableColumn>
          )}
        </TableHeader>
        <TableBody items={props.bankCardList}>
          {(item) => (
            <TableRow key={item.bankCardId}>
              {(columnKey) => (
                <TableCell>{renderCell(item, columnKey)}</TableCell>
              )}
            </TableRow>
          )}
        </TableBody>
      </Table>
    </>
  );
}
