/*
 * Copyright (c) Honor Device Co., Ltd. 2024-2024. All rights reserved.
 */

package com.hihonor.wallet.loan.client.general;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.hihonor.wallet.common.client.DmpClient;
import com.hihonor.wallet.common.constant.CommonConstant;
import com.hihonor.wallet.common.redis.LoanRedisUtil;
import com.hihonor.wallet.common.redis.RedisUtil;
import com.hihonor.wallet.common.util.*;
import com.hihonor.wallet.common.util.log.MdcUtil;
import com.hihonor.wallet.loan.client.config.LoanClientConfig;
import com.hihonor.wallet.loan.client.config.LoanClientConfigParam;
import com.hihonor.wallet.loan.client.dxm.constant.DxmConstant;
import com.hihonor.wallet.loan.client.general.model.dto.*;
import com.hihonor.wallet.loan.client.general.model.param.*;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;


import cn.hutool.core.collection.CollectionUtil;

import com.hihonor.wallet.common.client.OauthClient;
import com.hihonor.wallet.common.exception.BusinessException;
import com.hihonor.wallet.common.exception.WalletResultCode;
import com.hihonor.wallet.common.model.dto.oauth.UserRealNameDto;
import com.hihonor.wallet.common.util.log.LogUtil;
import com.hihonor.wallet.loan.client.general.constant.GeneralConstant;
import com.hihonor.wallet.loan.client.general.constant.VerifyResultStatus;
import com.hihonor.wallet.loan.client.BaseLoanService;
import com.hihonor.wallet.loan.client.constant.LoanClientConstant;
import com.hihonor.wallet.loan.client.model.dto.LoanApplyDto;
import com.hihonor.wallet.loan.client.model.dto.*;
import com.hihonor.wallet.loan.client.model.param.*;
import com.hihonor.wallet.loan.constant.LoanConstant;
import com.hihonor.wallet.loan.model.dto.RepayInfo;
import com.hihonor.wallet.loan.model.dto.*;
import com.hihonor.wallet.loan.model.param.PreLoanDataParam;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-02-26
 */
@Component(BaseLoanService.CLASS_NAME + LoanClientConstant.LoanTechProvider.GENERAL)
@RefreshScope
public class GeneralWrapClient extends BaseLoanService {

    private static final String PHONE_PREFIX = "0086";

    private static final String MODEL_SCORE_KEY = "udid";

    public static final Long MIN_LOAN_AMOUNT = 50000L;

    public static final String AGREEMENT = "AGREEMENT_LOAN";


    @Autowired
    private GeneralApi generalApi;

    @Autowired
    private OauthClient oauthClient;

    @Autowired
    private LoanClientConfig loanClientConfig;

    @Autowired
    private DmpClient dmpClient;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private LoanRedisUtil loanRedisUtil;

    @Value("${loan_min_amount.jd}")
    private Long jdLoanMinAmount;

    @Value("${loan_min_amount.lx}")
    private Long lxLoanMinAmount;

    @Value("${loan_min_amount.qf:500}")
    private Long qfLoanMinAmount;

    @Override
    public MobileModifyDto mobileModify(MobileModifyParam param) {
        GeneralModifyPhoneParam generalModifyPhoneParam = new GeneralModifyPhoneParam();
        BeanUtils.copyProperties(param, generalModifyPhoneParam);
        GeneralModifyPhoneDto generalModifyPhoneDto = generalApi.modifyPhone(generalModifyPhoneParam);
        MobileModifyDto mobileModifyDto = new MobileModifyDto();
        BeanUtils.copyProperties(generalModifyPhoneDto, mobileModifyDto);
        return mobileModifyDto;
    }

    @Override
    public BindUserDto bindUser(BindUserParam param) {
        GeneralUserBindParam generalUserBindParam = new GeneralUserBindParam();
        generalUserBindParam.setUserId(param.getUserId());
        generalUserBindParam.setCtfCode(param.getCtfCode());
        generalUserBindParam.setRealName(param.getRealName());
        generalUserBindParam.setMobileNo(param.getMobileNo());
        generalUserBindParam.setSupplier(param.getSupplier());
        LoanClientConfigParam loanClientConfigParam = loanClientConfig.getSpTsmConfig(param.getSupplier(), null);
        generalUserBindParam.setApiUserTagMap(processUserTag(GeneralConstant.USER_BIND_URI, loanClientConfigParam));
        generalUserBindParam.setApiModelScoreMap(getApiModelScore(param.getMobileNo(), param.getSupplier(), loanClientConfigParam));

        GeneralUserBindDto dto = generalApi.userBind(generalUserBindParam);
        BindUserDto bindUserDto = new BindUserDto();
        BeanUtils.copyProperties(dto, bindUserDto);
        bindUserDto.setAccess(false);
        if (dto.getAccess() == 1) {
            bindUserDto.setAccess(true);
        } else {
            covertFailCode(bindUserDto);
        }
        bindUserDto.setRefuseMsgData(dto.getRefuseMsgData());
        return bindUserDto;
    }

    @Override
    public QueryUserCreditInfoDto queryUserCreditInfo(QueryUserCreditInfoParam param) {
        GeneralParam generalParam = new GeneralParam();
        generalParam.setUserId(param.getUserId());
        generalParam.setSupplier(param.getSupplier());
        GeneralUserCreditInfoDto dto = generalApi.userCreditInfo(generalParam);
        if (dto == null) {
            return null;
        }
        QueryUserCreditInfoDto queryUserCreditInfoDto = new QueryUserCreditInfoDto();
        BeanUtils.copyProperties(dto, queryUserCreditInfoDto);
        if (Objects.nonNull(dto.getRepayInfo())) {
            RepayInfo repayInfo = new RepayInfo();
            BeanUtils.copyProperties(dto.getRepayInfo(), repayInfo);
            queryUserCreditInfoDto.setRepayInfo(repayInfo);
        }

        if (Objects.nonNull(dto.getOverdueInfo()) && Objects.nonNull(dto.getOverdueInfo().getOverdueAmount())) {
            queryUserCreditInfoDto.setOverdueInfo(dto.getOverdueInfo());
        }

        if (Objects.nonNull(dto.getTempLimitInfo())) {
            TempLimitInfo tempLimitInfo = new TempLimitInfo();
            GeneralTempLimitInfo generalTempLimitInfo = dto.getTempLimitInfo();
            tempLimitInfo.setValidTime(generalTempLimitInfo.getTempLimitValidTime());
            tempLimitInfo.setCreditLimit(generalTempLimitInfo.getTempCreditLimit());
            tempLimitInfo.setAvailableLimit(generalTempLimitInfo.getTempAvailableLimit());
            queryUserCreditInfoDto.setTempLimitInfo(tempLimitInfo);
        }
        if (CollectionUtil.isNotEmpty(dto.getProductInfos())) {
            List<ProductInfo> productInfoList = dto.getProductInfos().stream().map(productInfo -> {
                ProductInfo product = new ProductInfo();
                BeanUtils.copyProperties(productInfo, product);
                return product;
            }).collect(Collectors.toList());
            queryUserCreditInfoDto.setProductInfo(productInfoList);
        }

        return queryUserCreditInfoDto;
    }

    @Override
    public QueryAdjustInfoDto queryAdjustInfo(QueryAdjustInfoParam param) {
        GeneralQueryAdjustInfoParam generalQueryAdjustInfoParam = new GeneralQueryAdjustInfoParam();
        generalQueryAdjustInfoParam.setUserId(param.getUserId());
        generalQueryAdjustInfoParam.setOpenId(param.getOpenId());
        generalQueryAdjustInfoParam.setRepayNo(param.getTransNo());
        generalQueryAdjustInfoParam.setSupplier(param.getSupplier());
        GeneralQueryAdjustInfoDto dto = generalApi.queryAdjustInfo(generalQueryAdjustInfoParam);
        GeneralLimitAdjustInfo limitAdjustInfo = dto.getLimitAdjustInfo();
        CreditChangeDto creditChangeDto = new CreditChangeDto();
        GeneralRateAdjustInfo rateAdjustInfo = getMinNewDailyRateInfo(dto.getRateAdjustInfos());
        GeneralRateAdjustInfo tmpPriceInfo = getMinTempDayRateInfo(dto.getRateAdjustInfos());
        if (limitAdjustInfo != null) {
            creditChangeDto.setLimitChangeType(limitAdjustInfo.getLimitChangeType());
            creditChangeDto.setRemainLimit(limitAdjustInfo.getRemainLimit());
            creditChangeDto.setTotalAmount(limitAdjustInfo.getTotalAmount());
            creditChangeDto.setOldLimit(limitAdjustInfo.getBeforeTotalAmount());
            creditChangeDto.setTempLimitValidTime(limitAdjustInfo.getTempLimitValidTime());
            creditChangeDto.setTempLimitValidDays(TimeUtils.getDayDifference(limitAdjustInfo.getTempLimitValidTime()));
        }
        if (rateAdjustInfo != null && tmpPriceInfo != null) {
            double tmpDayRate = Double.parseDouble(tmpPriceInfo.getTempDayRate());
            double newDayRate = Double.parseDouble(rateAdjustInfo.getDayRate());
            if (tmpDayRate < newDayRate) {
                getTmpPriceInfo(creditChangeDto, tmpPriceInfo);
            } else {
                getRateAdjustInfo(creditChangeDto, rateAdjustInfo);
            }
        } else if (tmpPriceInfo != null) {
            getTmpPriceInfo(creditChangeDto, tmpPriceInfo);
        } else if (rateAdjustInfo != null) {
            getRateAdjustInfo(creditChangeDto, rateAdjustInfo);
        }

        QueryAdjustInfoDto queryAdjustInfoDto = new QueryAdjustInfoDto();
        queryAdjustInfoDto.setCreditChange(creditChangeDto);
        return queryAdjustInfoDto;
    }

    /**
     * 获取临价信息
     *
     * @param creditChangeDto creditChangeDto
     * @param tmpPriceInfo    tmpPriceInfo
     */
    private void getTmpPriceInfo(CreditChangeDto creditChangeDto, GeneralRateAdjustInfo tmpPriceInfo) {
        creditChangeDto.setNewApr(tmpPriceInfo.getTempApr());
        creditChangeDto.setNewDayRate(tmpPriceInfo.getTempDayRate());
        creditChangeDto.setOldDayRate(tmpPriceInfo.getBeforeDayRate());
        creditChangeDto.setOldApr(tmpPriceInfo.getBeforeApr());
        creditChangeDto.setTempDayRate(tmpPriceInfo.getTempDayRate());
        creditChangeDto.setTempApr(tmpPriceInfo.getTempApr());
        creditChangeDto.setRateChangeType(tmpPriceInfo.getRateChangeType());
        covertTmpPriceTime(creditChangeDto, tmpPriceInfo);
    }

    /**
     * 获取调价信息
     *
     * @param creditChangeDto creditChangeDto
     * @param rateAdjustInfo  rateAdjustInfo
     */
    private void getRateAdjustInfo(CreditChangeDto creditChangeDto, GeneralRateAdjustInfo rateAdjustInfo) {
        creditChangeDto.setNewApr(rateAdjustInfo.getApr());
        creditChangeDto.setNewDayRate(rateAdjustInfo.getDayRate());
        creditChangeDto.setOldApr(rateAdjustInfo.getBeforeApr());
        creditChangeDto.setOldDayRate(rateAdjustInfo.getBeforeDayRate());
        creditChangeDto.setRateChangeType(rateAdjustInfo.getRateChangeType());
    }

    /**
     * 获取临价时间
     *
     * @param creditChangeDto creditChangeDto
     * @param tmpPriceInfo    tmpPriceInfo
     */
    private void covertTmpPriceTime(CreditChangeDto creditChangeDto, GeneralRateAdjustInfo tmpPriceInfo) {
        creditChangeDto.setTempPriceDueTime(tmpPriceInfo.getTempPriceDueTime());
        creditChangeDto.setTempPriceValidDays(TimeUtils.getDayDifference(tmpPriceInfo.getTempPriceDueTime()));
    }

    /**
     * 获取 GeneralRateAdjustInfo
     *
     * @param generalRateAdjustInfos generalRateAdjustInfos
     * @return GeneralRateAdjustInfo
     */
    public static GeneralRateAdjustInfo getMinNewDailyRateInfo(List<GeneralRateAdjustInfo> generalRateAdjustInfos) {
        if (CollectionUtil.isEmpty(generalRateAdjustInfos)) {
            return null;
        }
        Optional<GeneralRateAdjustInfo> minRateInfo = generalRateAdjustInfos.stream()
                .filter(info -> info.getDayRate() != null) // 过滤掉null值
                .min(Comparator.comparingDouble(info -> Double.parseDouble(info.getTempDayRate())));
        return minRateInfo.orElse(null);
    }

    /**
     * 获取 tmpPriceInfos
     *
     * @param tmpPriceInfos tmpPriceInfos
     * @return GeneralTmpPriceInfo
     */
    public static GeneralRateAdjustInfo getMinTempDayRateInfo(List<GeneralRateAdjustInfo> tmpPriceInfos) {
        if (CollectionUtil.isEmpty(tmpPriceInfos)) {
            return null;
        }
        Optional<GeneralRateAdjustInfo> minPriceInfo = tmpPriceInfos.stream()
                .filter(info -> info.getTempDayRate() != null) // 过滤掉null值
                .min(Comparator.comparingDouble(info -> Double.parseDouble(info.getTempDayRate())));

        return minPriceInfo.orElse(null);
    }

    @Override
    public ApplyLimitDto applyLimit(ApplyLimitParam param) {
        GeneralCreditApplyParam limitApplyParam = new GeneralCreditApplyParam();
        limitApplyParam.setUserId(param.getUserId());
        limitApplyParam.setSupplier(param.getSupplier());
        limitApplyParam.setUserInfo(param.getUserInfo());
        limitApplyParam.setApplyNo(param.getApplyNo());
        if (CollectionUtil.isNotEmpty(param.getFileInfos())) {
            List<GeneralFileInfos> fileInfos = param.getFileInfos().stream().map(fileInfo -> {
                GeneralFileInfos dxmFileInfo = new GeneralFileInfos();
                BeanUtils.copyProperties(fileInfo, dxmFileInfo);
                return dxmFileInfo;
            }).collect(Collectors.toList());
            limitApplyParam.setFileInfos(fileInfos);
        }
        LoanClientConfigParam loanClientConfigParam = loanClientConfig.getSpTsmConfig(param.getSupplier(), null);
        limitApplyParam.setApiUserTagMap(processUserTag(GeneralConstant.LIMIT_APPLY_URI, loanClientConfigParam));
        limitApplyParam.setApiModelScoreMap(getApiModelScore(null, param.getSupplier(), loanClientConfigParam));

        GeneralCreditApplyDto dto = generalApi.limitApply(limitApplyParam);
        ApplyLimitDto applyLimitDto = new ApplyLimitDto();
        applyLimitDto.setResult(dto.getApplyStatus());
        applyLimitDto.setOutOrderNo(dto.getOutOrderNo());
        applyLimitDto.setRefuseMsg(dto.getRefuseMsg());
        applyLimitDto.setRefuseCode(dto.getRefuseCode());
        applyLimitDto.setDayRate(dto.getDayRate());
        applyLimitDto.setApr(dto.getApr());
        applyLimitDto.setRemainLimit(dto.getRemainLimit());
        applyLimitDto.setRefuseMsgData(dto.getRefuseMsgData());
        return applyLimitDto;
    }

    @Override
    public QueryCreditApplyResultDto queryCreditApplyResult(QueryCreditApplyResultParam param) {
        GeneralCreditApplyResultParam creditApplyResultParam = new GeneralCreditApplyResultParam();
        creditApplyResultParam.setApplyNo(param.getApplyNo());
        creditApplyResultParam.setUserId(param.getUserId());
        creditApplyResultParam.setSupplier(param.getSupplier());
        GeneralCreditApplyResultDto dto = generalApi.creditApplyResult(creditApplyResultParam);
        QueryCreditApplyResultDto queryCreditApplyResultDto = new QueryCreditApplyResultDto();
        covertCreditApplyResult(queryCreditApplyResultDto, dto);
        return queryCreditApplyResultDto;
    }

    @Override
    public LoanCalculateDto loanTrial(LoanCalculateParam param) {
        if (param.getLoanAmount() % 100 != 0 || param.getSupplier().equals(LoanConstant.Supplier.JD) && param.getLoanAmount() < jdLoanMinAmount || param.getSupplier().equals(LoanConstant.Supplier.LX) && param.getLoanAmount() < lxLoanMinAmount || param.getSupplier().equals(LoanConstant.Supplier.QF) && param.getLoanAmount() < qfLoanMinAmount) {
            throw new BusinessException(WalletResultCode.VALIDATED_PARAM, "loanAmount");
        }

        GeneralLoanTrialParam generalLoanTrialParam = new GeneralLoanTrialParam();
        BeanUtils.copyProperties(param, generalLoanTrialParam);
        GeneralLoanTrialDto generalLoanTrialDto = generalApi.loanTrial(generalLoanTrialParam);
        LogUtil.runSensitiveInfoLog("借款试算三方返回：" + JsonUtils.toJson(generalLoanTrialDto));
        LoanCalculateDto loanCalculateDto = new LoanCalculateDto();
        BeanUtils.copyProperties(generalLoanTrialDto, loanCalculateDto);
        List<RepayPlanTermDto> repayPlanTerms = generalLoanTrialDto.getRepayPlanTerms();
        List<RepayPlanTermDto> res = new ArrayList<>();
        for (RepayPlanTermDto item : repayPlanTerms) {
            RepayPlanTermDto repayPlanTermDto = new RepayPlanTermDto();
            BeanUtils.copyProperties(item, repayPlanTermDto);
            res.add(repayPlanTermDto);
        }
        List<ContractInfoDto> contractDtoList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(generalLoanTrialDto.getContract())){
            for (ContractDto contractDto: generalLoanTrialDto.getContract()){
                ContractInfoDto dto = new ContractInfoDto();
                if (Objects.isNull(contractDto.getDefaultChecked()) || Objects.isNull(contractDto.getContractName()) || Objects.isNull(contractDto.getContractUrl())){
                    throw new BusinessException(WalletResultCode.ILLEGAL_PARAM, "乐信返回的借款试算协议信息有误");
                }
                dto.setShow(contractDto.getDefaultChecked() != null && contractDto.getDefaultChecked().equals(1));
                dto.setContractName(contractDto.getContractName());
                dto.setContractUrl(contractDto.getContractUrl());
                contractDtoList.add(dto);
            }
           loanCalculateDto.setContract(contractDtoList);
        }

        loanCalculateDto.setRepayPlanTerms(res);
        return loanCalculateDto;
    }

    @Override
    public LoanVerifyListDto loanVerifyList(LoanVerifyListParam param) {
        LogUtil.runSensitiveInfoLog("借款交易鉴权卡包侧参数：" + JsonUtils.toJson(param));
        GeneralLoanVerifyListParam generalLoanVerifyListParam = new GeneralLoanVerifyListParam();
        BeanUtils.copyProperties(param, generalLoanVerifyListParam);
        LoanClientConfigParam loanClientConfigParam = loanClientConfig.getSpTsmConfig(param.getSupplier(), null);
        generalLoanVerifyListParam.setApiUserTagMap(processUserTag(GeneralConstant.LOAN_VERIFY_LIST_GENERAL_URI, loanClientConfigParam));
        generalLoanVerifyListParam.setApiModelScoreMap(getApiModelScore(null, param.getSupplier(), loanClientConfigParam));
        GeneralLoanVerifyListDto generalLoanVerifyListDto = generalApi.loanVerifyList(generalLoanVerifyListParam);
        LogUtil.runSensitiveInfoLog("借款交易鉴权三方返回：" + JsonUtils.toJson(generalLoanVerifyListDto));
        LoanVerifyListDto loanVerifyListDto = new LoanVerifyListDto();
        if (!CollectionUtil.isEmpty(generalLoanVerifyListDto.getVerifyList())) {
            generalLoanVerifyListDto.setVerifyList(getVerifyList(param.getSupplier(), generalLoanVerifyListDto.getVerifyList(), param.getSign()));
        }
        loanVerifyListDto.setVerifyList(generalLoanVerifyListDto.getVerifyList());
        LogUtil.runInfoLog("借款交易鉴权项为：{}", generalLoanVerifyListDto.getVerifyList());
        loanVerifyListDto.setRefuseMsg(generalLoanVerifyListDto.getRefuseMsg());
        loanVerifyListDto.setRefuseControlDays(generalLoanVerifyListDto.getRefuseControlDays());
        // 当前验证状态的转换
        loanVerifyListDto.setApplyStatus(generalLoanVerifyListDto.getStatus());
        loanVerifyListDto.setRefuseMsgData(generalLoanVerifyListDto.getRefuseMsgData());
        return loanVerifyListDto;
    }

    @Override
    public LoanApplyDto loanApply(LoanApplyParam param) {
        GeneralLoanApplyParam generalLoanApplyParam = new GeneralLoanApplyParam();
        BeanUtils.copyProperties(param, generalLoanApplyParam);
        generalLoanApplyParam.setCareer(LoanConstant.LoanApplyCareer.get(param.getCareer()));
        generalLoanApplyParam.setIncome(LoanConstant.LoanApplyIncome.get(param.getIncome()));
        LoanClientConfigParam loanClientConfigParam = loanClientConfig.getSpTsmConfig(param.getSupplier(), null);
        generalLoanApplyParam.setApiUserTagMap(processUserTag(GeneralConstant.LOAN_APPLY_URI, loanClientConfigParam));
        generalLoanApplyParam.setApiModelScoreMap(getApiModelScore(null, param.getSupplier(), loanClientConfigParam));
        GeneralLoanApplyDto generalLoanApplyDto = generalApi.loanApply(generalLoanApplyParam);
        LogUtil.runSensitiveInfoLog("借款申请提交三方返回：" + JsonUtils.toJson(generalLoanApplyDto));
        LoanApplyDto loanApplyDto = new LoanApplyDto();
        loanApplyDto.setApplyNo(generalLoanApplyDto.getOutOrderNo());
        loanApplyDto.setRefuseMsg(generalLoanApplyDto.getRefuseMsg());
        loanApplyDto.setRefuseCode(generalLoanApplyDto.getRefuseCode());
        loanApplyDto.setRefuseMsgData(generalLoanApplyDto.getRefuseMsgData());
        return loanApplyDto;
    }

    @Override
    public QueryLoanResultDto queryLoanResult(QueryLoanResultParam param) {
        GeneralLoanResultParam generalLoanResultParam = new GeneralLoanResultParam();
        BeanUtils.copyProperties(param, generalLoanResultParam);
        GeneralLoanResultDto generalLoanResultDto = generalApi.loanResult(generalLoanResultParam);
        LogUtil.runSensitiveInfoLog("借款结果查询返回：" + JsonUtils.toJson(generalLoanResultDto));
        QueryLoanResultDto queryLoanResultDto = new QueryLoanResultDto();
        BeanUtils.copyProperties(generalLoanResultDto, queryLoanResultDto);
        return queryLoanResultDto;
    }

    @Override
    public LoanRecordListDto queryLoanRecord(QueryLoanRecordParam param) {
        GeneralLoanRecordParam generalLoanRecordParam = new GeneralLoanRecordParam();
        BeanUtils.copyProperties(param, generalLoanRecordParam);
        GeneralLoanRecordListDto generalLoanRecordListDto = generalApi.loanRecord(generalLoanRecordParam);
        LogUtil.runInfoLog("generalLoanRecordListDto 参数为 ：{}",generalLoanRecordListDto);
        LoanRecordListDto loanRecordListDto = new LoanRecordListDto();
        BeanUtils.copyProperties(generalLoanRecordListDto, loanRecordListDto);
        LogUtil.runInfoLog("loanRecordListDto 参数为 ：{}",loanRecordListDto);
        return loanRecordListDto;
    }

    @Override
    public LoanRecordDetailDto queryLoanRecordDetail(QueryLoanRecordDetailParam param) {
        GeneralLoanRecordDetailParam generalLoanRecordDetailParam = new GeneralLoanRecordDetailParam();
        BeanUtils.copyProperties(param, generalLoanRecordDetailParam);
        GeneralLoanRecordDetailDto generalLoanRecordDetailDto = generalApi.loanRecordDetail(generalLoanRecordDetailParam);
        if (generalLoanRecordDetailDto != null && StringUtils.isEmpty(generalLoanRecordDetailDto.getOutOrderNo())) {
            return null;
        }
        LoanRecordDetailDto loanRecordDetailDto = new LoanRecordDetailDto();
        BeanUtils.copyProperties(generalLoanRecordDetailDto, loanRecordDetailDto);
        loanRecordDetailDto.setStatus(generalLoanRecordDetailDto.getStatus());
        loanRecordDetailDto.setTotalTerm(generalLoanRecordDetailDto.getTotalTerm());
        loanRecordDetailDto.setRepayMethod(generalLoanRecordDetailDto.getRepayMethod());
        loanRecordDetailDto.setApr(generalLoanRecordDetailDto.getApr());
        loanRecordDetailDto.setInstitutionNames(generalLoanRecordDetailDto.getInstitutionNames());
        if (CollectionUtil.isNotEmpty(generalLoanRecordDetailDto.getRepayPlanTerms())) {
            loanRecordDetailDto.setRepayPlanTerms(BeanUtilCopy.copyListProperties(generalLoanRecordDetailDto.getRepayPlanTerms(), RepayPlanTermDto::new,
                    (generalLoanRecordDetailDt, repayPlanTermDto) -> {
                        BeanUtils.copyProperties(generalLoanRecordDetailDt, repayPlanTermDto);
                        repayPlanTermDto.setOverdueAmt(generalLoanRecordDetailDt.getOverdueAmount());
                        repayPlanTermDto.setTermStatus(generalLoanRecordDetailDt.getTermStatus() == null ? 0 : generalLoanRecordDetailDt.getTermStatus());
                    })
            );
        }
        return loanRecordDetailDto;
    }

    @Override
    public Integer closeLoanOrder(CloseLoanOrderParam param) {
        GeneralCloseLoanOrderParam generalCloseLoanOrderParam = new GeneralCloseLoanOrderParam();
        BeanUtils.copyProperties(param, generalCloseLoanOrderParam);
        GeneralCloseLoanOrderDto generalCloseLoanOrderDto = generalApi.closeLoanOrder(generalCloseLoanOrderParam);
        if (generalCloseLoanOrderDto != null && generalCloseLoanOrderDto.getResult() != null){
            return generalCloseLoanOrderDto.getResult();
        }
        return 0;
    }

    @Override
    public List<RepayPlanDto> queryRepayPlan(QueryRepayPlanParam param) {
        GeneralRepayPlanParam generalRepayPlanParam = new GeneralRepayPlanParam();
        BeanUtils.copyProperties(param, generalRepayPlanParam);
        GeneralRepayPlanDto generalRepayPlanDto;
        try {
            generalRepayPlanDto = generalApi.repayPlan(generalRepayPlanParam);
        } catch (BusinessException e) {
            if (isUserLogOffErrorCode(e.getErrorCode())) {
                return Collections.emptyList();
            } else {
                throw new BusinessException(e.getErrorCode(), e.getErrorMessage());
            }
        }
        List<RepayPlanDto> result = new ArrayList<>(generalRepayPlanDto.getRecords());
        for (int i = 0; i < result.size(); i++) {
            generalRepayPlanDto.getRecords().get(i).setLoanOverDueDays(result.get(i).getOverdueDays());
            generalRepayPlanDto.getRecords().get(i).setLoanOverDueAmount(result.get(i).getOverdueAmount());
            for (int j = 0; j < result.get(i).getRepayPlanTerms().size(); j++) {
                generalRepayPlanDto.getRecords().get(i).getRepayPlanTerms().get(j).setLoanOverDueDays(result.get(i).getRepayPlanTerms().get(j).getOverdueDays());
            }
            List<RepayType> supportRepayTypes = convertToRepayTypeList(generalRepayPlanDto.getRecords().get(i).getSupportRepayType(), param, generalRepayPlanDto.getRecords().get(i));
            generalRepayPlanDto.getRecords().get(i).setSupportRepayTypeDetails(supportRepayTypes);
            Boolean currentDueDate = getCurrentDueDate(generalRepayPlanDto.getRecords().get(i));
            generalRepayPlanDto.getRecords().get(i).setCurrentDueDate(currentDueDate);
        }
        return generalRepayPlanDto.getRecords();
    }

    @Override
    public RepayCalculateDto repayCalculate(RepayCalculateParam param) {
        GeneralRepayCalculateParam generalRepayCalculateParam = new GeneralRepayCalculateParam();
        BeanUtils.copyProperties(param, generalRepayCalculateParam);
        generalRepayCalculateParam.setOpenId(param.getOpenId());
        generalRepayCalculateParam.setUserId(param.getUserId());
        generalRepayCalculateParam.setRepayTerm(param.getRepayTerm());
        generalRepayCalculateParam.setCouponNo(param.getCouponNo());
        generalRepayCalculateParam.setRepayAmount(null);
        if (param.getRepayPart()) {
            generalRepayCalculateParam.setTrialType(2);
            generalRepayCalculateParam.setRepayPart(true);
            generalRepayCalculateParam.setRepayAmount(param.getRepayAmount());
        }

        GeneralRepayCalculateDto dto = generalApi.repayCalculate(generalRepayCalculateParam);
        RepayCalculateDto repayCalculateDto = new RepayCalculateDto();
        BeanUtils.copyProperties(dto, repayCalculateDto);
        String nowDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        repayCalculateDto.setAccountDate(nowDate);
        repayCalculateDto.setTotalAmount(dto.getTotalAmount());
        repayCalculateDto.setRepayPlanTerms(dto.getRepayPlanTerms());
        repayCalculateDto.setCoupon(dto.getSelectedCoupons());
        repayCalculateDto.setUsableRepayCoupons(dto.getUsableCoupons());
        repayCalculateDto.setRepayTerms(dto.getRepayTerms());
        repayCalculateDto.setTotalDiscount(dto.getTotalReductionAmount());
        return repayCalculateDto;
    }

    @Override
    public ResignCheckDto resignCheck(ResignCheckParam param) {
        GeneralResignCheckParam generalResignCheckParam = new GeneralResignCheckParam();
        BeanUtils.copyProperties(param, generalResignCheckParam);
        generalResignCheckParam.setOutOrderNo(param.getOrderId());
        GeneralResignCheckDto generalResignCheckDto = generalApi.resignCheck(generalResignCheckParam);
        ResignCheckDto dto = new ResignCheckDto();
        dto.setNeedResign(0);
        BeanUtils.copyProperties(generalResignCheckDto, dto);
        return dto;
    }

    @Override
    public VerifyBindSmsDto verifyBindSms(VerifyBindSmsParam param) {
        GeneralBindSmsVerifyParam verifyParam = new GeneralBindSmsVerifyParam();
        BeanUtils.copyProperties(param, verifyParam);
        GeneralBindSmsVerifyDto dto = generalApi.bindSmsVerify(verifyParam);
        VerifyBindSmsDto verifyBindSmsDto = new VerifyBindSmsDto();
        verifyBindSmsDto.setResult(true);
        verifyBindSmsDto.setMessage(dto.getVerifyMsg());
        if (dto.getVerifyResult() != 0) {
            verifyBindSmsDto.setResult(false);
        }
        verifyBindSmsDto.setVerifyResult(dto.getVerifyResult());
        String msg = covertVerifyMsg(dto.getVerifyResult(), VerifyResultStatus.BindSmsVerifyVerifyResultStatus.values());
        if (StringUtils.isNotBlank(msg)) {
            verifyBindSmsDto.setMessage(msg);
        }
        return verifyBindSmsDto;
    }

    @Override
    public RepayDoDto repayDo(RepayDoParam param) {
        GeneralRepayDoParam generalRepayDoParam = buildRepayDoParam(param);
        GeneralRepayDoDto dto = generalApi.repayDo(generalRepayDoParam);
        RepayDoDto doDto = new RepayDoDto();
        doDto.setRepayOrderId(dto.getOutRepayNo());
        return doDto;
    }


    @Override
    public QueryRepayResultDto queryRepayResult(QueryRepayResultParam param) {
        GeneralRepayResultParam repayResultParam = new GeneralRepayResultParam();
        BeanUtils.copyProperties(param, repayResultParam);
        repayResultParam.setRepayNo(param.getTransNo());
        GeneralRepayResultDto dto = generalApi.repayResult(repayResultParam);
        QueryRepayResultDto queryRepayResultDto = new QueryRepayResultDto();
        BeanUtils.copyProperties(dto, queryRepayResultDto);
        if (queryRepayResultDto.getRepayStatus() != null && queryRepayResultDto.getRepayStatus().equals(LoanConstant.RepayStatus.REPAY_SUCCESS)) {
            queryRepayResultDto.setRepayResult("success");
        }
        queryRepayResultDto.setTransNo(dto.getOutRepayNo());
        return queryRepayResultDto;
    }

    @Override
    public List<RepayDto> queryRepayRecord(QueryRepayRecordParam param) {
        GeneralRepayRecordParam generalRepayRecordParam = new GeneralRepayRecordParam();
        BeanUtils.copyProperties(param, generalRepayRecordParam);
        generalRepayRecordParam.setOutOrderNo(param.getLoanOrderId());
        GeneralRepayRecordDto generalRepayRecordDto = generalApi.repayRecord(generalRepayRecordParam);
        return BeanUtilCopy.copyListProperties(generalRepayRecordDto.getRecords(), RepayDto::new, (RepayRecordDto repayRecordDto, RepayDto repayDto) -> {
            repayDto.setRepayAmount(repayRecordDto.getRepayAmount());
            repayDto.setRepayTime(convertTime(repayRecordDto.getRepayTime()));
            repayDto.setRepayStatus(repayRecordDto.getRepayStatus());
        });
    }

    @Override
    public QuerySettlementDto querySettlement(QuerySettlementParam param) {
        GeneralSettlementQueryParam queryParam = new GeneralSettlementQueryParam();
        BeanUtils.copyProperties(param, queryParam);
        queryParam.setTransNoList(param.getApplyNos());
        LogUtil.runInfoLog("查询是否可发送结清证明 start 参数：{}",queryParam);
        GeneralSettlementQueryDto queryDto = generalApi.settlementQuery(queryParam);
        LogUtil.runInfoLog("查询是否可发送结清证明 end 结果为：{}",queryDto);
        QuerySettlementDto res = new QuerySettlementDto();
//        if (queryDto != null && queryDto.getSettlementList() != null) {
        if (queryDto != null && queryDto.getRecords() != null) {
            List<GeneralSettlementList> settlementList = queryDto.getRecords();
            res.setSettlementList(BeanUtilCopy.copyListProperties(settlementList, SettlementList::new));
        } else {
            res.setSettlementList(new ArrayList<>());
        }
        return res;
    }

    @Override
    public SendSettlementDto sendSettlement(SendSettlementParam param) {
        SendSettlementDto res = new SendSettlementDto();
        GeneralSettlementSendParam sendParam = new GeneralSettlementSendParam();
        BeanUtils.copyProperties(param, sendParam);
//        sendParam.setOutOrderNoList(param.getOutOrderNos());
        GeneralSettlementSendDto dxmSettlementSendDto = generalApi.settlementSend(sendParam);
        BeanUtils.copyProperties(dxmSettlementSendDto, res);
        return res;
    }

    @Override
    public QueryBindBankcardListDto queryBindBankcardList(QueryBindBankcardListParam param) {
        QueryBindBankcardListDto result = new QueryBindBankcardListDto();
        GeneralParam generalParam = new GeneralParam();
        BeanUtils.copyProperties(param, generalParam);
        GeneralBindBankCardListDto generalBindBankCardListDto = generalApi.bindBankCardList(generalParam);
        List<GeneralBindBankCard> dxmBindBankCardList = generalBindBankCardListDto.getRecords();
        if (CollectionUtil.isNotEmpty(dxmBindBankCardList)) {
            List<BankCardDto> bankCardDtoList = new ArrayList<>();
            dxmBindBankCardList.stream().forEach(item -> {
                BankCardDto bankCardDto = new BankCardDto();
                BeanUtils.copyProperties(item, bankCardDto);
                bankCardDtoList.add(bankCardDto);
            });
            result.setBankCardListDto(bankCardDtoList);
        }
        return result;
    }

    @Override
    public QuerySupportBankcardListDto querySupportBankcardList(QuerySupportBankcardListParam param) {
        QuerySupportBankcardListDto result = new QuerySupportBankcardListDto();
        GeneralParam generalParam = new GeneralParam();
        generalParam.setUserId(param.getUserId());
        generalParam.setOutOrderNo(param.getOutOrderNo());
        generalParam.setSupplier(param.getSupplier());
        GeneralSupportBankCardListDto generalSupportBankCardListDto = generalApi.supportBankCardList(generalParam);
        if (CollectionUtil.isNotEmpty(generalSupportBankCardListDto.getRecords())) {
            List<BankDto> bankDtoList = new ArrayList<>();
            generalSupportBankCardListDto.getRecords().stream().forEach(item -> {
                BankDto bankDto = new BankDto();
                BeanUtils.copyProperties(item, bankDto);
                bankDtoList.add(bankDto);
            });
            result.setBankDtos(bankDtoList);
        }
        return result;
    }

    @Override
    public BindBankcardDto bindBankcard(BindBankcardParam param) {
        BindBankcardDto result = new BindBankcardDto();
        GeneralBindBankCardParam generalBindBankCardParam = new GeneralBindBankCardParam();
        BeanUtils.copyProperties(param, generalBindBankCardParam);
        GeneralBindBankCardDto generalBindBankCardDto = generalApi.bindBankCard(generalBindBankCardParam);
        result.setBankCardId(generalBindBankCardDto.getBankCardId());
        result.setVerifyMsg(generalBindBankCardDto.getVerifyMsg());

        String verifyMsg = covertVerifyMsg(generalBindBankCardDto.getVerifyResult(), VerifyResultStatus.BindBandCardVerifyResultStatus.values());
        if (StringUtils.isNotBlank(verifyMsg)) {
            result.setVerifyMsg(verifyMsg);
        }
        result.setVerifyResult(generalBindBankCardDto.getVerifyResult());
        result.setBankCode(generalBindBankCardDto.getBankCode());
        result.setBankName(generalBindBankCardDto.getBankName());
        result.setSingleLimit(generalBindBankCardDto.getSingleLimit());
        return result;
    }

    private Integer covertVerifyResult(int code, VerifyResultStatus[] values) {
        for (VerifyResultStatus value : values) {
            if (value.getHonorStatus().equals(code)) {
                return value.getHonorStatus();
            }
        }
        return 99;
    }

    private String covertVerifyMsg(int code, VerifyResultStatus[] values) {
        for (VerifyResultStatus value : values) {
            if (value.getHonorStatus().equals(code)) {
                return value.getMsg();
            }
        }
        return null;
    }

    @Override
    public List<ContractInfoDto> queryAgreement(QueryAgreementParam param) {
        GeneralAgreementQueryParam generalAgreementQueryParam = new GeneralAgreementQueryParam();
        generalAgreementQueryParam.setUserId(param.getUserId());
        generalAgreementQueryParam.setSupplier(param.getSupplier());
        generalAgreementQueryParam.setBusinessType(param.getType());
        generalAgreementQueryParam.setApplyNo(param.getApplyNo());
        if (param.getType() == LoanConstant.Contract.CREDIT) {
            UserRealNameDto userRealNameDto = oauthClient.getRealName(RequestUtils.getUid(), false);
            if (Objects.isNull(userRealNameDto) || Objects.isNull(userRealNameDto.getRealName())) {
                throw new BusinessException(WalletResultCode.CREDIT_APPLY_USER_INFO_NOT_EXIST);
            }
            UserInfoParam userInfoParam = new UserInfoParam();
            userInfoParam.setCtfCode(userRealNameDto.getCtfCode());
            userInfoParam.setRealName(userRealNameDto.getRealName());
            userInfoParam.setMobileNo(RequestUtils.getMobileNo());
            generalAgreementQueryParam.setUserInfo(userInfoParam);
        } else if (param.getType() == LoanConstant.Contract.REDISTRIBUTE) {
            generalAgreementQueryParam.setOutOrderNo(param.getOutOrderNo());
        } else if (param.getType() == LoanConstant.Contract.LOAN) {
            PreLoanDataParam preLoanDataParam = param.getPreLoanData();
            GeneralPreLoanData generalPrevLoanData = new GeneralPreLoanData();
            generalPrevLoanData.setLoanAmount(preLoanDataParam.getLoanAmount());
            generalPrevLoanData.setTotalTerm(preLoanDataParam.getTotalTerm());
            generalPrevLoanData.setLoanUse(preLoanDataParam.getLoanUse());
            generalPrevLoanData.setRepayMethod(preLoanDataParam.getRepayMethod());
            generalPrevLoanData.setBankCardId(preLoanDataParam.getBankCardId());
            generalPrevLoanData.setFirstRepayDate(preLoanDataParam.getFirstRepayDate());
            generalAgreementQueryParam.setPreLoanData(generalPrevLoanData);
        }
        GeneralAgreementQueryDto generalAgreementQueryDto = generalApi.agreementQuery(generalAgreementQueryParam);
        return BeanUtilCopy.copyListProperties(generalAgreementQueryDto.getRecords(), ContractInfoDto::new, (GeneralContract generalContract, ContractInfoDto contractDto) -> {
            contractDto.setShow(generalContract.getDefaultChecked() == LoanConstant.ContractShow.CHECK);
            contractDto.setForceRead(generalContract.getForceRead() == LoanConstant.ContractForceRead.FORCE_READ);
        });
    }

    @Override
    public List<ContractDetailDto> queryContractDetail(QueryContractDetailParam param) {
        GeneralContractDetailQueryParam generalContractDetailQueryParam = new GeneralContractDetailQueryParam();
        generalContractDetailQueryParam.setUserId(param.getUserId());
        generalContractDetailQueryParam.setSupplier(param.getSupplier());
        if (param.getType() == LoanConstant.ProtocolType.PROTOCOL_TYPE_URL) {
            generalContractDetailQueryParam.setType(GeneralConstant.ProtocolType.PROTOCOL_TYPE_URL);
            generalContractDetailQueryParam.setOutOrderNo(param.getContractId());
        } else {
            generalContractDetailQueryParam.setType(param.getType());
            generalContractDetailQueryParam.setContractId(param.getContractId());
        }
        GeneralContractDetailQueryDto generalContractDetailQueryDto = generalApi.contractDetailQuery(generalContractDetailQueryParam);
        return BeanUtilCopy.copyListProperties(generalContractDetailQueryDto.getRecords(), ContractDetailDto::new);
    }

    @Override
    public QueryLoanCouponDto queryLoanCoupon(QueryLoanCouponParam param) {
        return null;
    }

    @Override
    public QueryAllCouponListDto queryAllCouponList(QueryAllCouponListParam param) {
        return null;
    }

    @Override
    public String sendSmsCode(SendSmsCodeForClientParam param) {
        GeneralSendSmsCodeParam generalSendSmsCodeParam = new GeneralSendSmsCodeParam();
        BeanUtils.copyProperties(param, generalSendSmsCodeParam);
        GeneralSendSmsCodeDto generalSendSmsCodeDto = generalApi.sendSmsCode(generalSendSmsCodeParam);
        return generalSendSmsCodeDto.getSerialNo();
    }

    @Override
    public VerifySmsCodeDto verifySmsCode(VerifySmsCodeParam param) {
        GeneralVerifySmsCodeParam generalVerifySmsCodeParam = new GeneralVerifySmsCodeParam();
        generalVerifySmsCodeParam.setCode(param.getSmsCode());
        generalVerifySmsCodeParam.setOpenId(param.getOpenId());
        generalVerifySmsCodeParam.setMobileNo(param.getMobileNo());
        generalVerifySmsCodeParam.setUserId(param.getUserId());
        generalVerifySmsCodeParam.setSerialNo(param.getSerialNo());
        generalVerifySmsCodeParam.setSupplier(param.getSupplier());
        generalVerifySmsCodeParam.setType(param.getType());
        generalApi.verifySmsCode(generalVerifySmsCodeParam);
        VerifySmsCodeDto verifySmsCodeDto = new VerifySmsCodeDto();
        verifySmsCodeDto.setResult(true);
        return verifySmsCodeDto;
    }

    @Override
    public IdCardCheckDto idCardCheck(IdCardCheckParam param) {
        GeneralIdCardCheckParam generalIdCardCheckParam = new GeneralIdCardCheckParam();
        BeanUtils.copyProperties(param, generalIdCardCheckParam);
        GeneralIdCardCheckDto dto = generalApi.checkIdCard(generalIdCardCheckParam);
        IdCardCheckDto idCardCheckDto = new IdCardCheckDto();
        IdCardImageResultDto frontImageResult = new IdCardImageResultDto();
        IdCardImageResultDto backImageResult = new IdCardImageResultDto();
        BeanUtils.copyProperties(dto.getFrontImage(), frontImageResult);
        BeanUtils.copyProperties(dto.getBackImage(), backImageResult);
        idCardCheckDto.setBackImage(backImageResult);
        idCardCheckDto.setFrontImage(frontImageResult);
        return idCardCheckDto;
    }

    /**
     * 获取 逾期信息
     *
     * @param param param
     * @return OverdueInfoDto
     */
    public OverdueInfoDto getOverdueInfo(QueryRepayPlanParam param) {
        GeneralRepayPlanParam repayPlanParam = new GeneralRepayPlanParam();
        repayPlanParam.setUserId(param.getUserId());
        repayPlanParam.setOpenId(param.getOpenId());
        repayPlanParam.setSupplier(param.getSupplier());
        GeneralRepayPlanDto dto = generalApi.repayPlan(repayPlanParam);
        Map<Boolean, Integer> result = dto.getRecords().stream()
                .filter(item -> item.getOverdueAmount() != null)
                .collect(Collectors.partitioningBy(
                        item -> item.getOverdueAmount() > 0,
                        Collectors.summingInt(GeneralRepayPlan -> 1)
                ));

        Optional<Long> maxValue = dto.getRecords().stream()
                .map(RepayPlanDto::getOverdueDays)
                .filter(Objects::nonNull) // 过滤掉值为 null 的元素
                .max(Long::compareTo);

        Long sum = dto.getRecords().stream()
                .filter(item -> item.getOverdueAmount() != null)
                .mapToLong(RepayPlanDto::getOverdueAmount)
                .sum();

        Long overduePenalty = dto.getRecords().stream().flatMap(obj -> obj.getRepayPlanTerms().stream()).mapToLong(objB -> {
            long termPrinPenalty = objB.getTermPrinPenalty() != null && objB.getTermPrinPenalty() >= 0 ? objB.getTermPrinPenalty() : 0;
            long termInterPenalty = objB.getTermInterPenalty() != null && objB.getTermInterPenalty() >= 0 ? objB.getTermInterPenalty() : 0;
            return termPrinPenalty + termInterPenalty;
        }).sum();
        OverdueInfoDto overdueInfoDto = new OverdueInfoDto();
        overdueInfoDto.setOverdueAmount(sum);
        overdueInfoDto.setOverdueOrder(result.get(true));
        overdueInfoDto.setOverduePenalty(overduePenalty);
        maxValue.ifPresent(overdueInfoDto::setOverdueDays);
        return overdueInfoDto;
    }

    @Override
    public UserCancelAccountDto userCancelAccount(UserCancelAccountParam param) {
        GeneralUserCancelAccountParam generalUserCancelAccount = new GeneralUserCancelAccountParam();
        generalUserCancelAccount.setUserId(param.getUserId());
        generalUserCancelAccount.setSupplier(param.getSupplier());
        UserCancelAccountDto userCancelAccountDto = new UserCancelAccountDto();
        try {
            GeneralUserCancelAccountDto dto = generalApi.userCancelAccount(generalUserCancelAccount);
            if (dto == null || dto.getLogoffAllowed() == null) {
                userCancelAccountDto.setLogoffAllowed(false);
                userCancelAccountDto.setDesc("cp接口异常");
                return userCancelAccountDto;
            } else if (!dto.getLogoffAllowed()) {
                userCancelAccountDto.setLogoffAllowed(false);
                userCancelAccountDto.setDesc(dto.getReason());
                return userCancelAccountDto;
            }
            userCancelAccountDto.setLogoffAllowed(true);
        } catch (BusinessException e) {
            if (isUserLogOffErrorCode(e.getErrorCode())) {
                userCancelAccountDto.setLogoffAllowed(true);
                return userCancelAccountDto;
            } else {
                throw new BusinessException(e.getErrorCode(), e.getErrorMessage());
            }
        }
        return userCancelAccountDto;
    }

    @Override
    public UserLogoffDto userLogoff(UserLogoffParam param) {
        GeneralUserLogoffParam logoffParam = new GeneralUserLogoffParam();
        BeanUtils.copyProperties(param, logoffParam);
        GeneralUserLogoffDto dto = generalApi.userLogoff(logoffParam);
        UserLogoffDto userLogoffDto = new UserLogoffDto();
        userLogoffDto.setIsLogoff(dto.getLogoffResult());
        userLogoffDto.setDesc(dto.getLogoffErrDesc());
        return userLogoffDto;
    }

    @Override
    public QueryAddCreditUrlDto queryAddUrl(QueryAddCreditUrlParam param) {
        //未接入此接口的cp直接返回
        if (param.getSupplier() == LoanConstant.Supplier.JD || param.getSupplier() == LoanConstant.Supplier.LX || param.getSupplier() == LoanConstant.Supplier.QF) {
            return new QueryAddCreditUrlDto();
        }
        GeneralQueryAddCreditUrlParam generalQueryAddCreditUrlParam = new GeneralQueryAddCreditUrlParam();
        generalQueryAddCreditUrlParam.setUserId(param.getUserId());
        generalQueryAddCreditUrlParam.setSupplier(param.getSupplier());
        GeneralQueryAddCreditUrlDto dxmQueryAddUrlDto = generalApi.queryAddUrl(generalQueryAddCreditUrlParam);
        QueryAddCreditUrlDto queryAddUrlDto = new QueryAddCreditUrlDto();
        BeanUtils.copyProperties(dxmQueryAddUrlDto, queryAddUrlDto);
        return queryAddUrlDto;
    }

    @Override
    public QueryWithholdSignUrlDto querySignUrl(QueryWithholdSignUrlParam param) {
        GeneralSignUrlParam generalSignUrlParam = new GeneralSignUrlParam();
        generalSignUrlParam.setUserId(param.getUserId());
        generalSignUrlParam.setReturnUrl(param.getReturnUrl());
        generalSignUrlParam.setSupplier(param.getSupplier());
        GeneralSignUrlDto generalSignUrlDto = generalApi.querySignUrl(generalSignUrlParam);
        QueryWithholdSignUrlDto queryWithholdSignUrlDto = new QueryWithholdSignUrlDto();
        BeanUtils.copyProperties(generalSignUrlDto, queryWithholdSignUrlDto);
        return queryWithholdSignUrlDto;
    }

    /**
     * 获取all优惠券
     *
     * @param param param
     * @return
     */
    @Override
    public List<CouponListDto> allCouponList(AllCouponListParam param) {
        List<CouponListDto> couponListDtos = new ArrayList<>();
        GeneralAllCouponListParam generalAllCouponListParam = new GeneralAllCouponListParam();
        generalAllCouponListParam.setUserId(param.getUserId());
        generalAllCouponListParam.setSupplier(param.getSupplier());
        GeneralAllCouponListDto generalAllCouponListDto = generalApi.allCouponList(generalAllCouponListParam);
        if (Objects.isNull(generalAllCouponListDto) || CollectionUtil.isEmpty(generalAllCouponListDto.getRecords())) {
            return couponListDtos;
        }
        return BeanUtilCopy.copyListProperties(generalAllCouponListDto.getRecords(), CouponListDto::new);
    }

    @Override
    public List<CouponListDto> loanUsableCounpons(QueryUsableCounponsParam param) {
        GeneralLoanCouponParam generalLoanCouponParam = new GeneralLoanCouponParam();
        BeanUtils.copyProperties(param, generalLoanCouponParam);
        GeneralLoanCouponDto generalLoanCouponDto = generalApi.loanCoupon(generalLoanCouponParam);
        List<CouponListDto> couponListDtos = new ArrayList<>();
        if (Objects.isNull(generalLoanCouponDto) || CollectionUtil.isEmpty(generalLoanCouponDto.getRecords())) {
            return couponListDtos;
        }
        return BeanUtilCopy.copyListProperties(generalLoanCouponDto.getRecords(), CouponListDto::new);
    }


    /**
     * 构造主动还款请求体
     *
     * @param param param
     * @return
     */
    private GeneralRepayDoParam buildRepayDoParam(RepayDoParam param) {
        GeneralRepayDoParam generalRepayDoParam = new GeneralRepayDoParam();
        BeanUtils.copyProperties(param, generalRepayDoParam);
        generalRepayDoParam.setRepayTerms(param.getRepayTerms());
        generalRepayDoParam.setRepayType(param.getRepayType());
        generalRepayDoParam.setBankCardId(param.getBankCardId());
        generalRepayDoParam.setUserId(param.getUserId());
        generalRepayDoParam.setOpenId(param.getOpenId());
        generalRepayDoParam.setRepayAmount(param.getTotalAmount());
        generalRepayDoParam.setRepayNo(param.getTransNo());
        // todo 这里要修改一下
        generalRepayDoParam.setOutOrderNo(param.getRepayItemList().get(0).getOutOrderNo());
        return generalRepayDoParam;
    }

    /**
     * 还款类型转换
     *
     * @param repayType repayType
     * @return String
     */
    private static String covertRepayType(Integer repayType) {
        if (repayType == 1) {
            return GeneralConstant.RepayType.EARLY_REPAY;
        }
        return GeneralConstant.RepayType.NORMAL_REPAY;
    }

    /**
     * 还款状态转换
     *
     * @param repayStatus repayStatus
     * @return
     * @throws BusinessException
     */
    public static Integer covertRepayStatus(String repayStatus) {
        if (StringUtils.isBlank(repayStatus)) {
            throw new BusinessException("dxm接口repayStatus返回为空");
        }
        if (StringUtils.equals(repayStatus, GeneralConstant.RepayStatus.REPAYING)) {
            return LoanConstant.RepayStatus.REPAYING;
        } else if (StringUtils.equals(repayStatus, GeneralConstant.RepayStatus.FAIL)) {
            return LoanConstant.RepayStatus.REPAY_FAIL;
        } else if (StringUtils.equals(repayStatus, GeneralConstant.RepayStatus.PART)) {
            return LoanConstant.RepayStatus.REPAY_PART_SUCCESS;
        }

        return LoanConstant.RepayStatus.REPAY_SUCCESS;
    }

    /**
     * 授信状态转换
     *
     * @param applyStatus applyStatus
     * @return Integer
     */
    private static Integer covertApplyResult(String applyStatus) {
        if (StringUtils.equals(applyStatus, GeneralConstant.CreditApplyResult.AUDITING)) {
            return LoanConstant.CreditStatus.UNDER_REVIEW;
        } else if (StringUtils.equals(applyStatus, GeneralConstant.CreditApplyResult.PASS)) {
            return LoanConstant.CreditStatus.APPROVED;
        } else if (StringUtils.equals(applyStatus, GeneralConstant.CreditApplyResult.REFUSE)) {
            return LoanConstant.CreditStatus.REJECT;
        } else if (StringUtils.equals(applyStatus, GeneralConstant.CreditApplyResult.WAIT)) {
            return LoanConstant.CreditStatus.UNDER_REVIEW;
        }
        return LoanConstant.CreditStatus.UNDER_REVIEW;
    }

    /**
     * 新老客标识转换
     *
     * @param identify identify
     * @return Integer
     */
    public static Integer covertIdentify(String identify) {
        if (StringUtils.isBlank(identify)) {
            return null;
        } else if (StringUtils.equals(identify, GeneralConstant.Identify.NEW)) {
            return LoanConstant.Identify.NEW;
        }
        return LoanConstant.Identify.OLD;
    }

    /**
     * 额度状态转换
     *
     * @param limitStatus limitStatus
     * @return Integer
     */
    private static Integer covertLimitStatus(String limitStatus) {
        if (StringUtils.isBlank(limitStatus)) {
            return null;
        } else if (StringUtils.equals(limitStatus, GeneralConstant.LimitStatus.REFUSE)) {
            return LoanConstant.LimitStatus.REFUSE;
        } else if (StringUtils.equals(limitStatus, GeneralConstant.LimitStatus.EXPIRED)) {
            return LoanConstant.LimitStatus.EXPIRED;
        } else if (StringUtils.equals(limitStatus, GeneralConstant.LimitStatus.UPGRADING)) {
            return LoanConstant.LimitStatus.UPGRADING;
        } else if (StringUtils.equals(limitStatus, GeneralConstant.LimitStatus.AUDITING)) {
            return LoanConstant.LimitStatus.AUDITING;
        }
        return LoanConstant.LimitStatus.ACTIVE;
    }

    /**
     * 额度使用状态转换
     *
     * @param status status
     * @return Integer
     */
    private static Integer covertLimitUseErrStatus(String status) {
        if (StringUtils.isBlank(status)) {
            return null;
        } else if (StringUtils.equals(status, GeneralConstant.LimitUseErrStatus.LENDING_ERR)) {
            return LoanConstant.LimitUseErrStatus.LENDING_ERR;
        } else if (StringUtils.equals(status, GeneralConstant.LimitUseErrStatus.OTHER_ERR)) {
            return LoanConstant.LimitUseErrStatus.OTHER_ERR;
        } else if (StringUtils.equals(status, GeneralConstant.LimitUseErrStatus.OVERDUE_ERR)) {
            return LoanConstant.LimitUseErrStatus.OVERDUE_ERR;
        }
        return LoanConstant.LimitUseErrStatus.UNAVAILABLE_ERR;
    }

    /**
     * 借款状态转换
     */
    public static Integer covertLoanStatus(String status, boolean isExistVerifyList) {
        if (status == null) {
            return null;
        } else if (status.equals(GeneralConstant.LoanStatus.APPLYING)) {
            if (isExistVerifyList) {
                return LoanConstant.LoanStatus.NEEDAUTH;
            }
            return LoanConstant.LoanStatus.REVIEWING;
        } else if (status.equals(GeneralConstant.LoanStatus.FAIL)) {
            return LoanConstant.LoanStatus.FAIL;
        } else if (status.equals(GeneralConstant.LoanStatus.REFUSE)) {
            return LoanConstant.LoanStatus.REFUSE;
        } else if (status.equals(GeneralConstant.LoanStatus.SUCCESS)) {
            return LoanConstant.LoanStatus.SUCCESS;
        } else if (status.equals(GeneralConstant.LoanStatus.LENDING)) {
            return LoanConstant.LoanStatus.REVIEWING;
        } else if (status.equals(GeneralConstant.LoanStatus.CANCEL)) {
            return LoanConstant.LoanStatus.CANSUME;
        }
        return null;
    }

    /**
     * 借款鉴权状态转换
     */
    private Integer covertLoanVerifyStatus(String status) {
        if (StringUtils.isBlank(status)) {
            return null;
        } else if (StringUtils.equals(status, GeneralConstant.LoanVerifyStatus.REFUSE)) {
            return LoanConstant.LoanVerifyStatus.REFUSE;
        } else if (StringUtils.equals(status, GeneralConstant.LoanVerifyStatus.VERIFY)) {
            return LoanConstant.LoanVerifyStatus.NEEDAUTH;
        } else if (StringUtils.equals(status, GeneralConstant.LoanVerifyStatus.WAIT)) {
            return LoanConstant.LoanVerifyStatus.WAITING;
        }
        return null;
    }

    /**
     * 还款方式转换
     *
     * @param method method
     * @return Integer
     */
    public static Integer covertRepayMethod(String method) {
        if (StringUtils.isBlank(method)) {
            return null;
        } else if (StringUtils.equals(method, GeneralConstant.RepayMethod.FIXED_INSTALLMENT)) {
            return LoanConstant.RepayMethod.FIXED_INSTALLMENT;
        } else if (StringUtils.equals(method, GeneralConstant.RepayMethod.FIXED_INTEREST_ON_SCHEDULE)) {
            return LoanConstant.RepayMethod.FIXED_INTEREST_ON_SCHEDULE;
        } else if (StringUtils.equals(method, GeneralConstant.RepayMethod.FIXED_PRINCIPAL)) {
            return LoanConstant.RepayMethod.FIXED_PRINCIPAL;
        }
        return LoanConstant.RepayMethod.FIXED_XXHB_TYPE_NEW;
    }

    /**
     * 将卡包的还款方式转换成度小满的还款方式
     */
    public static String covertRepayMethod(Integer status) {
        if (status == null) {
            return null;
        } else if (status == LoanConstant.RepayMethod.FIXED_INSTALLMENT) {
            return GeneralConstant.RepayMethod.FIXED_INSTALLMENT;
        } else if (status == LoanConstant.RepayMethod.FIXED_INTEREST_ON_SCHEDULE) {
            return GeneralConstant.RepayMethod.FIXED_INTEREST_ON_SCHEDULE;
        } else if (status == LoanConstant.RepayMethod.FIXED_PRINCIPAL) {
            return GeneralConstant.RepayMethod.FIXED_PRINCIPAL;
        } else if (status == LoanConstant.RepayMethod.FIXED_XXHB_TYPE_NEW) {
            return GeneralConstant.RepayMethod.FIXED_XXHB_TYPE_NEW;
        }
        return null;
    }

    /**
     * 调整结果类型
     */
    private Integer getCreditChangeType(String oldLimit, String nowLimit) {
        if (StringUtils.isBlank(oldLimit) || StringUtils.isBlank(nowLimit)) {
            return null;
        }
        if (Double.parseDouble(oldLimit) > Double.parseDouble(nowLimit)) {
            return LoanConstant.CreditChangeType.LOW;
        }
        return LoanConstant.CreditChangeType.HEIGHT;
    }

    /**
     * 参数转换
     *
     * @param queryCreditApplyResultDto queryCreditApplyResultDto
     * @param resultDto                 resultDto
     */
    private void covertCreditApplyResult(QueryCreditApplyResultDto queryCreditApplyResultDto, GeneralCreditApplyResultDto resultDto) {
        BeanUtils.copyProperties(resultDto, queryCreditApplyResultDto);
        queryCreditApplyResultDto.setApplyStatus(resultDto.getApplyStatus());
        if (!Objects.isNull(resultDto.getRefuseControlTime())) {
            queryCreditApplyResultDto.setRefuseControlDays(Math.toIntExact(resultDto.getRefuseControlTime() / (60 * 60 * 24)));
        }
        if (Objects.nonNull(resultDto.getTotalAvailableLimit())) {
            queryCreditApplyResultDto.setTotalAvailableLimit(resultDto.getTotalAvailableLimit().toString());
        }
        if (Objects.nonNull(resultDto.getTotalCreditLimit())) {
            queryCreditApplyResultDto.setTotalCreditLimit(resultDto.getTotalCreditLimit().toString());
        }
        if (resultDto.getTempLimitInfo() != null) {
            GeneralTempLimitInfo tempLimitInfo = resultDto.getTempLimitInfo();
            queryCreditApplyResultDto.setTempLimitValidTime(tempLimitInfo.getTempLimitValidTime());
            if (Objects.nonNull(tempLimitInfo.getTempCreditLimit())) {
                queryCreditApplyResultDto.setTempLimitCredit(tempLimitInfo.getTempCreditLimit().toString());
            }

            if (Objects.nonNull(tempLimitInfo.getTempAvailableLimit())) {
                queryCreditApplyResultDto.setTempLimitAvailable(tempLimitInfo.getTempAvailableLimit().toString());
            }
            queryCreditApplyResultDto.setTempApr(tempLimitInfo.getTempApr());
            queryCreditApplyResultDto.setTempDayRate(tempLimitInfo.getTempDayRate());
            queryCreditApplyResultDto.setTempPriceValidTime(tempLimitInfo.getTempPriceValidTime());
        }
    }

    /**
     * 将秒级时间戳差值转换为天数，如果有小数则向上取整。
     *
     * @param timeStampStart 开始的时间戳，单位为秒。
     * @param timeStampEnd   结束的时间戳，单位为秒。
     * @return 转换后的天数。
     */
    public static Integer convertToDaysWithCeilOnDecimal(long timeStampStart, long timeStampEnd) {
        // 计算时间戳差值
        long timeStampDiff = timeStampEnd - timeStampStart;
        double days = (double) timeStampDiff / TimeUnit.DAYS.toSeconds(1);
        // 如果days与其向下取整的结果不相等，则说明有小数部分，需要向上取整
        return Math.toIntExact(days > (long) days ? (long) days + 1 : (long) days);
    }


    private static long convertToTimestamp(String dateString) {
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
            // 解析日期字符串
            Date date = dateFormat.parse(dateString);
            // 获取秒级时间戳
            return date.getTime() / 1000;
        } catch (ParseException e) {
            LogUtil.runErrorLog("convertToTimestamp ERROR,{}", e);
        }
        return 0;
    }

    private String convertTime(Long time){
        if(time == null) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 将时间戳转换为 Date 对象
        Date date = new Date(time);
        // 格式化 Date 对象为指定格式的字符串
        return sdf.format(date);
    }

    /**
     * 状态码转换
     *
     * @param dto
     */
    private void covertFailCode(BindUserDto dto) {
        MdcUtil.putSourceFailCodeAndReason(dto.getFailCode(), dto.getFailReason());
        if (StringUtils.isNotEmpty(dto.getFailCode()) && GeneralConstant.BIND_FAIL_CODE.containsKey(dto.getFailCode())) {
            dto.setFailReason(GeneralConstant.BIND_FAIL_CODE.get(dto.getFailCode()));
            dto.setFailCode("170224");
        } else {
            dto.setFailCode("170224");
            dto.setFailReason("暂无法提供服务");
        }
    }

    public List<RepayType> convertToRepayTypeList(List<Integer> integerList, QueryRepayPlanParam param, RepayPlanDto dto) {
        List<RepayType> repayTypeList = createRepayTypeList(integerList);
        if (dto.getStatus() == GeneralConstant.RepayPlanStatus.restricted) {
            disableAllRepayTypes(repayTypeList);
        }
        // LX的还款日当天全部结清需要置灰
        if (param.getSupplier() == LoanConstant.Supplier.LX) {
            List<RepayPlanTermDto> filteredTerms = dto.getRepayPlanTerms().stream()
                    .filter(term -> term.getTermNo() == dto.getCurrentTerm())
                    .collect(Collectors.toList());
            // 获取日期当前
            String currentDate = getCurrentDate("yyyyMMdd");
            if (!filteredTerms.isEmpty() && currentDate.equals(filteredTerms.get(0).getShouldRepayDate())) {
                repayTypeList.forEach(repayType -> {
                    if (repayType.getType() == DxmConstant.repaymentType.fullSettlement) {
                        repayType.setDisabled(true);
                    }
                });
            }
        }
        return repayTypeList;
    }

    private List<RepayType> createRepayTypeList(List<Integer> integerList) {
        return integerList.stream()
                .map(type -> {
                    RepayType repayType = new RepayType();
                    repayType.setType(type);
                    repayType.setDisabled(false);
                    return repayType;
                })
                .collect(Collectors.toList());
    }

    private void disableAllRepayTypes(List<RepayType> repayTypeList) {
        repayTypeList.forEach(repayType -> repayType.setDisabled(true));
    }

    private String getCurrentDate(String format) {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        return sdf.format(new Date());
    }

    private List<String> getVerifyList(Integer supplier, List<String> verifyList, Integer sign) {
        List<String> result = new ArrayList<>();
        LoanClientConfigParam loanClientConfigParam = loanClientConfig.getSpTsmConfig(supplier, null);
        List<String> filterVerifyList = loanClientConfigParam.getFilterVerifyList();

        if (supplier != null && CollectionUtil.isNotEmpty(filterVerifyList)) {
            result = filterVerifyList.stream().filter(verifyList::contains).collect(Collectors.toList());
        }

        if (!Objects.isNull(sign) && sign.equals(1) && result.contains(AGREEMENT)){
            result.remove(AGREEMENT);
        }
        return result;
    }

    private Boolean getCurrentDueDate(RepayPlanDto dto) {
        String todayDate = getCurrentDate("yyyyMMdd");
        return dto.getRepayPlanTerms().stream()
                .anyMatch(repayPlanTermDto -> todayDate.equals(repayPlanTermDto.getShouldRepayDate()));
    }

    public String processUserTag(String method, LoanClientConfigParam loanClientConfigParam) {
        String tagValue = null;
        String apiUserTagMethod = loanClientConfigParam.getApiUserTagMethod();
        if (StringUtils.isBlank(apiUserTagMethod)) {
            return null;
        }
        List<String> methodList = Arrays.asList(apiUserTagMethod.split(","));

        if (!StringUtils.isBlank(loanClientConfigParam.getApiUserTagName()) && methodList.contains(method)) {
            tagValue = dmpClient.queryUserReferTagValue(RequestUtils.getUid(), loanClientConfigParam.getApiUserTagName());
        }
        LogUtil.runInfoLog("获取到的标签值为{}", tagValue);
        return tagValue;
    }



    private String getApiModelScore(String mobileNo, Integer supplier, LoanClientConfigParam loanClientConfigParam) {
        if (loanClientConfigParam.getApiModelScoreEnabled() != null && loanClientConfigParam.getApiModelScoreEnabled()) {
            String modelScoreKey = null;
            if (MODEL_SCORE_KEY.equals(loanClientConfigParam.getApiModelScoreKey())) {
                modelScoreKey = MDC.get(CommonConstant.MDC_MODEL_SCORE_DEVICEID);
            } else {
                if (StringUtils.isBlank(mobileNo)) {
                    mobileNo = RequestUtils.getMobileNo();
                }
                modelScoreKey = DigestUtils.sha256Hex(PHONE_PREFIX + mobileNo);
            }


            String key = "";
            if (supplier == LoanConstant.Supplier.JD) {
                key = LoanConstant.LOAN_MODEL_SCORE_JD_KEY_PREFIX + modelScoreKey;
            } else if (supplier == LoanConstant.Supplier.LX) {
                key = LoanConstant.LOAN_MODEL_SCORE_LX_KEY_PREFIX + modelScoreKey;
            } else {
                return null;
            }
            String value;
            LogUtil.runInfoLog("模型分的key值是:{}", key);
            try {
                if (loanClientConfigParam.getApiModelScoreCache() != null && loanClientConfigParam.getApiModelScoreCache().equals(1)) {
                    LogUtil.runInfoLog("使用新的专属模型分redis");
                    value = loanRedisUtil.get(key);
                } else {
                    value = redisUtil.get(key);
                }
                LogUtil.runSensitiveInfoLog("获取到的模型分为:{}", value);
            } catch (Exception e) {
                LogUtil.runErrorLog("Error accessing Redis: " + e.getMessage());
                return null;
            }
            return StringUtils.isBlank(value) ? null : value;
        } else {
            return null;
        }
    }

    private boolean isUserLogOffErrorCode(int errorCode) {
        return errorCode == 200002 || errorCode == 200003;
    }
    
}
