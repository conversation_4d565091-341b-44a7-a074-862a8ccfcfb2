/*
 * Copyright (c) Honor Device Co., Ltd. 2024-2024. All rights reserved.
 */

package com.hihonor.wallet.loan.client.model.dto;

import lombok.Data;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-02-28
 */
@Data
public class QueryCreditApplyResultDto {
    /**
     * 额度申请结果
     */
    private Integer applyStatus;

    /**
     * DXM授信流水号
     */
    private String outOrderNo;

    /**
     * 授信拒绝管控期，单位：天
     */
    private Integer refuseControlDays;

    /**
     * 拒绝原因
     */
    private String refuseInfo;

    /**
     * 授信总额度
     */
    private Long creditLimit;

    /**
     * 原授信总额度(再分发后)
     */
    private Long originCreditLimit;

    /**
     * 额度类型
     */
    private Integer limitType;

    /**
     * 可用额度，单位：分
     */
    private Long remainLimit;

    /**
     * 额度失效日期
     */
    private String limitExpireDate;

    /**
     * 用户类型，老用户返回
     */
    private Integer identity;

    /**
     * 临价日利率
     */
    private String tempDayRate;

    /**
     * 临价年利率
     */
    private String tempApr;

    /**
     * 总可用额度（临额+固额总和）
     */
    private String totalAvailableLimit;

    /**
     * 总授信额度（临额+固额总和）
     */
    private String totalCreditLimit;

    /**
     * 临额有效期
     */
    private String tempLimitValidTime;

    /**
     * 临额授信额度
     */
    private String tempLimitCredit;

    /**
     * 临价截止时间
     */
    private String tempPriceValidTime;

    /**
     * 临额可用额度
     */
    private String tempLimitAvailable;

    /**
     * 拒绝原因码
     */
    private String refuseCode;

    /**
     * 拒绝原因
     */
    private String refuseMsg;

    /**
     * 日利率
     */
    private String dayRate;

    /**
     * 月利率
     */
    private String monthRate;

    /**
     * 年利率
     */
    private String apr;

    /**
     * 授信申请失败原因，成功无需返回,需要入库统计字段
     */
    private String refuseMsgData;
}
