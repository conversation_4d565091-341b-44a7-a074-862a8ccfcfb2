package com.hihonor.wallet.loan.client.config;

import lombok.Data;

@Data
public class SignResourceConfigParam {
    /**
     * 签约状态
     */
    private Integer signStatus;

    /**
     * 资源位是否启用（false隐藏）
     */
    private Boolean enable;

    /**
     * 签约资源位标题
     */
    private String title;

    /**
     * 这是签约资源位的说明文案
     */
    private String description;

    /**
     * 图标的url路径
     */
    private String icon;

    /**
     * 按钮的url路径
     */
    private String button;

    /**
     * 气泡文案
     */
    private String bubbleText;

}
