package com.hihonor.wallet.loan.controller;


import com.hihonor.wallet.common.model.ResponseResult;
import com.hihonor.wallet.loan.model.dto.ContractDetailDto;
import com.hihonor.wallet.loan.model.dto.ContractInfoDto;
import com.hihonor.wallet.loan.model.dto.ContractInfoRecordDto;
import com.hihonor.wallet.loan.model.param.ContractDetailParam;
import com.hihonor.wallet.loan.model.param.ContractListParam;
import com.hihonor.wallet.loan.service.ContractService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * <AUTHOR>
 */
@RestController("contractController")
@RequestMapping("/api/contract")
public class ContractController {


    @Autowired
    private ContractService contractService;

    /**
     * 获取协议列表
     * @param param
     * @return
     */
    @PostMapping("/list")
    public ResponseResult<List<ContractInfoDto>> getContractList(@Validated @RequestBody ContractListParam param) {
        List<ContractInfoDto> contractList = contractService.getContractList(param);
        return ResponseResult.success(contractList);
    }

    /**
     * 获取已签协议详情
     * @param param
     * @return
     */
    @PostMapping("/detail")
    public ResponseResult<List<ContractDetailDto>> getContractDetail(@Validated @RequestBody ContractDetailParam param) {
        List<ContractDetailDto> contractDetails = contractService.getContractDetail(param);
        return ResponseResult.success(contractDetails);
    }

    /**
     * 获取协议列表v2
     * @param param
     * @return
     */
    @PostMapping("/v2/list")
    public ResponseResult<ContractInfoRecordDto> getContractListV2(@Validated @RequestBody ContractListParam param) {
        ContractInfoRecordDto contractList = contractService.getContractListV2(param);
        return ResponseResult.success(contractList);
    }

}
