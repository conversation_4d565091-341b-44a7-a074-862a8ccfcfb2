/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.wallet.loan.controller;

import com.hihonor.wallet.common.config.nacos.LoanClientConfigJsonListener;
import com.hihonor.wallet.common.model.BaseRequest;
import com.hihonor.wallet.common.model.ResponseResult;
import com.hihonor.wallet.common.model.dto.config.DictItemDto;

import com.hihonor.wallet.common.security.SecurityVerifyIgnore;
import com.hihonor.wallet.loan.mapper.model.dto.SupplierListDto;
import com.hihonor.wallet.loan.model.dto.FormDto;
import com.hihonor.wallet.loan.model.dto.LoanAdActivityQueryDto;
import com.hihonor.wallet.loan.model.dto.LoanSupplierDto;
import com.hihonor.wallet.loan.model.dto.YoyoEntryDto;
import com.hihonor.wallet.loan.model.param.DictQueryParam;
import com.hihonor.wallet.loan.model.param.FormQueryParam;
import com.hihonor.wallet.loan.model.param.LoanActivityQueryParam;
import com.hihonor.wallet.loan.service.ContractService;
import com.hihonor.wallet.loan.service.DictItemService;
import com.hihonor.wallet.loan.service.FormService;
import com.hihonor.wallet.loan.service.LoanAdActivityService;
import com.hihonor.wallet.loan.service.LoanSupplierService;
import com.hihonor.wallet.loan.service.YoyoEntryService;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;


/**
 * 配置服务
 *
 * <AUTHOR>
 * @since 2024-02-18 11:18:17
 */
@Api(value = "config.ConfigController", tags = {"配置"})
@RestController("configController")
@RequestMapping("/api/config")
public class ConfigController {

	@Resource
	private FormService formService;

    @Resource
    private DictItemService dictItemService;

    @Resource
    private LoanSupplierService loanSupplierService;

    @Autowired
    private ContractService contractService;

    @Autowired
    private YoyoEntryService yoyoEntryService;

    @Autowired
    private LoanAdActivityService loanAdActivityService;

    @Resource
    private LoanClientConfigJsonListener loanClientConfigJsonListener ;

    /**
     * 表单配置
     */
    @ApiOperation(value = "表单配置")
    @PostMapping("/getFormSetting")
    public ResponseResult<FormDto> list(@Validated @RequestBody FormQueryParam queryParam) {
        FormDto formDto = formService.getFormSetting(queryParam);
        return ResponseResult.success(formDto);
    }

    /**
     * 字典配置
     */
    @ApiOperation(value = "字典配置")
    @PostMapping("/getDictMap")
    @SecurityVerifyIgnore(ignoreAccessTokenVerfy = true)
    public ResponseResult<Map<String, List<DictItemDto>>> getDictMap(@Validated @RequestBody DictQueryParam queryParam) {
        return ResponseResult.success(dictItemService.getDictMap(queryParam));
    }

    /**
     * 查询首页小贷严选推荐
     */
    @ApiOperation(value = "查询首页小贷严选推荐")
    @PostMapping("/suggest/list")
    @SecurityVerifyIgnore(ignoreAccessTokenVerfy = true)
    public ResponseResult<List<LoanSupplierDto>> getSuggestList() {
        return ResponseResult.success(loanSupplierService.getSuggestList());
    }

    /**
     * 获取白名单列表
     */
    @ApiOperation(value = "获取协议白名单列表")
    @PostMapping("/domainWhitelist")
    public ResponseResult<List<String>> getDomainWhitelist() {
        return ResponseResult.success(contractService.getDomainWhitelist());
    }

    /**
     * 查询CP配置
     */
    @ApiOperation(value = "查询CP配置")
    @PostMapping("/getCpConfig")
    public ResponseResult<LoanSupplierDto> getCpConfig(@Validated @RequestBody BaseRequest baseRequest) {
        return ResponseResult.success(loanSupplierService.getCpConfig());
    }

    /**
     * 查询API接入的借贷渠道列表
     */
    @ApiOperation(value = "查询API接入的借贷渠道列表")
    @PostMapping("/apiSupplier/list")
    @SecurityVerifyIgnore(ignoreAccessTokenVerfy = true)
    public ResponseResult<List<LoanSupplierDto>> getApiSupplierList() {
        return ResponseResult.success(loanSupplierService.getApiSupplierList());
    }

    /**
     * 获取yoyo还款助手卡片入口配置
     */
    @PostMapping("/yoyoEntry")
    @SecurityVerifyIgnore(ignoreAccessTokenVerfy = true)
    public ResponseResult<YoyoEntryDto> getYoyoEntry() {
        return ResponseResult.success(yoyoEntryService.getYoyoEntry());
    }

    /**
     * 根据活动编码查询小贷运营活动配置
     *
     * @param param 查询参数
     * @return List<LoanAdActivityDetailDto>
     */
    @PostMapping("/getLoanAdActivityConfig")
    @SecurityVerifyIgnore(ignoreAccessTokenVerfy = true)
    public ResponseResult<LoanAdActivityQueryDto>
        getLoanAdActivityConfig(@Validated @RequestBody LoanActivityQueryParam param) {
        LoanAdActivityQueryDto loanAdActivityAddDto = loanAdActivityService.getLoanAdActivityConfig(param);
        return ResponseResult.success(loanAdActivityAddDto);
    }


    @PostMapping("/getClientConfig")
    @SecurityVerifyIgnore(ignoreAccessTokenVerfy = true)
    public ResponseResult getLoanClientConfig() {
        return ResponseResult.success(loanClientConfigJsonListener.getData().getConfigMap());
    }
}
