/*
 * Copyright (c) Honor Device Co., Ltd. 2024-2024. All rights reserved.
 */

package com.hihonor.wallet.loan.client.general;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.hihonor.wallet.common.exception.BusinessException;
import com.hihonor.wallet.common.exception.WalletResultCode;
import com.hihonor.wallet.common.util.log.LogUtil;
import com.hihonor.wallet.common.util.log.MdcUtil;
import com.hihonor.wallet.loan.client.general.config.GeneralErrorCodeMappingConfig;
import com.hihonor.wallet.loan.client.general.model.dto.GeneralBaseDto;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-08-12
 */
@Component
public class GeneralErrorCodeConverter {
    @Autowired
    private GeneralErrorCodeMappingConfig generalErrorCodeMappingConfig;

    private static final Integer CTF_CODE_ERROR = 200201;

    private static final Integer NAME_ERROR = 200202;

    private static final Integer AGE_DOES_NOT_MEET_REQUIREMENTS = 200203;

    private static final Integer ID_CARD_EXPIRED = 200204;

    /**
     * 借款金额验证未通过
     */
    private static final Integer LOAN_AMOUNT_VERIFY_FAIL = 200801;

    /**
     * 借款用户流程状态异常
     */
    private static final Integer USER_PROCESS_STATUS_ABNORMAL_LOAN = 201001;

    /**
     * 订单已失效，请重新发起一笔新的订单
     */
    private static final Integer ORDER_INVALID_LOAN = 201002;

    /**
     * 可用额度不足
     */
    private static final Integer CREDIT_NOT_ENOUGH = 200802;

    /**
     * 不支持该借款期数，请重新选择
     */
    private static  final Integer LOAN_TERM_NOT_SUPPORT = 200803;

    /**
     * 不支持该借款类型，请重新选择
     */
    private static final Integer LOAN_TYPE_NOT_SUPPORT = 200804;

    /**
     * 当前订单不支持该优惠券
     */
    private static final Integer CUR_ORDER_NOT_SUPPORT_COUPON = 200805;

    /**
     *当前订单优惠券不支持该期数
     */
    private static final Integer CUR_COUPON_NOT_SUPPORT_TERM = 200806;

    /**
     * 当前订单优惠券活动未开始
     */
    private static final Integer CUR_ORDER_COUPON_NOT_START = 200807;

    /**
     * 当前订单优惠券已过期
     */
    private static final Integer CUR_ORDER_COUPON_EXPIRE = 200808;

    /**
     * 本期借据已结清，请勿重复还款
     */
    private static final Integer ALREADY_PAYOFF_NOT_REPEAT_REPAY = 201701;
    /**
     * 重复还款
     */
    private static final Integer REPEAT_REPAY = 201702;

    /**
     * 新锁期借据不可部分还当期
     */
    private static final Integer LOCK_NOT_PART_REPAY_CUR = 201703;
    /**
     * 新锁期借据不可部分还未来期
     */
    private static final Integer LOCK_NOT_PART_REPAY_FEATURE = 201704;

    /**
     * 未查询到相关交易
     */
    private static final Integer NOT_FOUND_RELATION_TRANSACTION = 201705;

    /**
     * 优惠金额与实际不符
     */
    private static final Integer DISCOUNT_AMOUNT_NOT_MATCH_ACTUAL_AMOUNT = 201706;

    /**
     * 还款金额过大
     */
    private static final Integer REPAY_AMOUNT_TOO_BIG = 201707;

    /**
     * 存在未结束的还款订单
     */
    private static final Integer EXIST_NOT_FINISH_REPAY_ORDER = 201708;

    /**
     * 新锁期借据部分，已被拦截
     */
    private static final Integer LOCK_PART_INTERCEPT = 201709;

    /**
     * 优惠券不符合使用规则
     */
    private static final Integer COUPON_NOT_COMPLY_RULE = 201710;

    /**
     * 优惠券已使用
     */
    private static final Integer COUPON_USED = 201711;

    /**
     * 优惠券已过期
     */
    private static final Integer COUPON_EXPIRED = 201712;

    /**
     * 优惠券不存在
     */
    private static final Integer COUPON_NOT_EXIST = 201713;

    // --------------------------------------------------------------借款申请提交-------------------------------------------------------
    /**
     * 借款失败，您的申请评估未通过
     */
    private static final Integer LOAN_FAIL_1 = 201003;

    private static final Integer LOAN_FAIL_2 = 201004;

    private static final Integer LOAN_FAIL_3 = 201005;
    private static final Integer LOAN_FAIL_4 = 201006;
    private static final Integer LOAN_FAIL_5 = 201013;
    private static final Integer LOAN_FAIL_6 = 201014;
    private static final Integer LOAN_FAIL_7 = 201015;
    /**
     * 9要素信息不完整，请重新上传
     */
    private static final Integer LOAN_INFO_NOT_ENOUGH_1 = 201007;
    /**
     * 证件审核不通过，请重新上传
     */
    private static final Integer LOAN_DOCUMENT_REVIEW_FAIL_1 = 201008;
    private static final Integer LOAN_DOCUMENT_REVIEW_FAIL_2 = 201009;
    private static final Integer LOAN_DOCUMENT_REVIEW_FAIL_3 = 201010;

    private static final Integer LOAN_DOCUMENT_REVIEW_FAIL_4 = 201019;
    private static final Integer LOAN_DOCUMENT_REVIEW_FAIL_5 = 201021;
    private static final Integer LOAN_DOCUMENT_REVIEW_FAIL_6 = 201022;

    /**
     * 证件审核中，请稍后重试
     */
    private static final Integer LOAN_DOCUMENT_REVIEWING = 201011;

    /**
     * 用户流程状态异常
     */
    private static final Integer USER_PROCESS_STATUS_ABNORMAL_LOAN_1 = 201017;
    private static final Integer USER_PROCESS_STATUS_ABNORMAL_LOAN_2 = 201018;

    /**
     * 人脸对比失败，请重新上传
     */
    private static final Integer FACE_CHECK_FAIL_1 = 201020;
    private static final Integer FACE_CHECK_FAIL_2 = 201023;

    /**
     * 不支持当前期数，请更换后重试
     */
    private static final Integer LOAN_CUR_TERM_NOT_SUPPORT = 201024;

    /**
     * 贷款金额过小，请重新输入
     */
    private static final Integer LOAN_AMOUNT_TOO_SMALL = 201025;

    /**
     * 银行卡校验失败，请重新绑卡
     */
    private static final Integer BANKCARD_CHECK_FAIL_1 = 201026;
    private static final Integer BANKCARD_CHECK_FAIL_2 = 201027;

    /**
     * 当前订单不支持该优惠券
     */
    private static final Integer CUR_ORDER_NOT_SUPPORT_COUPON_1 = 201029;

    /**
     * 订单已失效，请重新发起一笔新订单
     */
    private static final Integer ORDER_EXPIRE = 201030;

    // -------------------------------------------------------------------借款状态查询-------------------------------------------------
    /**
     * 无效的订单号
     */
    private static final Integer INVALID_ORDER_NUMBER = 200005;


    //  -----------------------------------------------------------------短信相关-------------------------------------------------------
    /**
     * 发送失败，请确认信息后重试
     */
    private static final Integer SEND_MESSAGE_FAIL = 203104;

    /**
     * 发送频繁，请1分钟后再发送
     */
    private static final Integer SEND_MESSAGE_FREQUENT = 203102;

    /**
     * 验证频繁，请稍后重试
     */
    private static final Integer VERIFY_MESSAGE_FREQUENT = 203203;

    /**
     * 验证码过期
     */
    private static final Integer VERIFY_CODE_EXPIRE = 203202;

    /**
     * 验证码错误
     */
    private static final Integer VERIFY_CODE_ERROR = 203201;

    // ------------------------------------------------------- 还款试算-----------------------------------------------------------
    /**
     * 无效操作
     */
    private static final Integer INVALID_OPERATION = 200004;

    /**
     * 新锁期借据部分，已被拦截
     */
    private static final Integer NEW_LOCK_UP_INTERCEPTED = 201709;

    // ----------------------------------------------------- 提交主动还款------------------------------------------------
    /**
     * 支付失败，请确认信息后重试
     */
    private static final Integer PAY_FAIL = 201714;

    private static final Integer PAY_FAIL_1 = 201715;
    private static final Integer PAY_FAIL_2 = 201716;
    private static final Integer PAY_FAIL_3 = 201717;

    private static final Integer SETTLEMENT_SEND_TOO_FREQUENT = 12841071;
    private static final Integer USER_NOT_EXIST = 200002;
    private static final Integer USER_LOG_OFF = 200003;

    /**
     * 余额不足，请使用其他支付方式
     */
    private static final Integer INSUFFICIENT_BALANCE = 201718;




    private final Map<Integer, WalletResultCode> errorCodeMapping;

    /**
     * GeneralErrorCodeConverter
     */
    public GeneralErrorCodeConverter() {
        // 初始化映射表，定义错误码的转换规则
        errorCodeMapping = new HashMap<>();
        errorCodeMapping.put(CTF_CODE_ERROR, WalletResultCode.REAL_NAME_INVALID);
        errorCodeMapping.put(NAME_ERROR, WalletResultCode.REAL_NAME_INVALID);
        errorCodeMapping.put(AGE_DOES_NOT_MEET_REQUIREMENTS, WalletResultCode.VALIDATED_PARAM);
        errorCodeMapping.put(ID_CARD_EXPIRED, WalletResultCode.VALIDATED_PARAM);
        // 添加更多的错误码转换规则...

        // 借款试算
        errorCodeMapping.put(LOAN_AMOUNT_VERIFY_FAIL, WalletResultCode.LOAN_TRAIL_ERROR);
        errorCodeMapping.put(CREDIT_NOT_ENOUGH, WalletResultCode.LOAN_CREDIT_NOT_ENOUGH);
        errorCodeMapping.put(LOAN_TERM_NOT_SUPPORT, WalletResultCode.LOAN_TERM_NOT_SUPPORT);
        errorCodeMapping.put(LOAN_TYPE_NOT_SUPPORT, WalletResultCode.LOAN_TYPE_NOT_SUPPORT);
        errorCodeMapping.put(CUR_ORDER_NOT_SUPPORT_COUPON, WalletResultCode.CUR_ORDER_NOT_SUPPORT_COUPON);
        errorCodeMapping.put(CUR_COUPON_NOT_SUPPORT_TERM, WalletResultCode.CUR_COUPON_NOT_SUPPORT_TERM);
        errorCodeMapping.put(CUR_ORDER_COUPON_NOT_START, WalletResultCode.CUR_ORDER_COUPON_NOT_START);
        errorCodeMapping.put(CUR_ORDER_COUPON_EXPIRE, WalletResultCode.CUR_ORDER_COUPON_EXPIRE);

        // 身份证校验


        // 交易鉴权

        // 借款申请提交
        errorCodeMapping.put(LOAN_FAIL_1, WalletResultCode.LOAN_FAIL);
        errorCodeMapping.put(LOAN_FAIL_2, WalletResultCode.LOAN_FAIL);
        errorCodeMapping.put(LOAN_FAIL_3, WalletResultCode.LOAN_FAIL);
        errorCodeMapping.put(LOAN_FAIL_4, WalletResultCode.LOAN_FAIL);
        errorCodeMapping.put(LOAN_FAIL_5, WalletResultCode.LOAN_FAIL);
        errorCodeMapping.put(LOAN_FAIL_6, WalletResultCode.LOAN_FAIL);
        errorCodeMapping.put(LOAN_FAIL_7, WalletResultCode.LOAN_FAIL);

        errorCodeMapping.put(LOAN_INFO_NOT_ENOUGH_1, WalletResultCode.LOAN_INFO_NOT_ENOUGH);

        errorCodeMapping.put(LOAN_DOCUMENT_REVIEW_FAIL_1, WalletResultCode.LOAN_DOCUMENT_REVIEW_FAIL);
        errorCodeMapping.put(LOAN_DOCUMENT_REVIEW_FAIL_2, WalletResultCode.LOAN_DOCUMENT_REVIEW_FAIL);
        errorCodeMapping.put(LOAN_DOCUMENT_REVIEW_FAIL_3, WalletResultCode.LOAN_DOCUMENT_REVIEW_FAIL);
        errorCodeMapping.put(LOAN_DOCUMENT_REVIEW_FAIL_4, WalletResultCode.LOAN_DOCUMENT_REVIEW_FAIL);
        errorCodeMapping.put(LOAN_DOCUMENT_REVIEW_FAIL_5, WalletResultCode.LOAN_DOCUMENT_REVIEW_FAIL);
        errorCodeMapping.put(LOAN_DOCUMENT_REVIEW_FAIL_6, WalletResultCode.LOAN_DOCUMENT_REVIEW_FAIL);

        errorCodeMapping.put(LOAN_DOCUMENT_REVIEWING, WalletResultCode.LOAN_DOCUMENT_REVIEWING);
        errorCodeMapping.put(USER_PROCESS_STATUS_ABNORMAL_LOAN_1, WalletResultCode.USER_PROCESS_STATUS_ABNORMAL_LOAN);
        errorCodeMapping.put(USER_PROCESS_STATUS_ABNORMAL_LOAN_2, WalletResultCode.USER_PROCESS_STATUS_ABNORMAL_LOAN);
        errorCodeMapping.put(FACE_CHECK_FAIL_1, WalletResultCode.FACE_CHECK_FAIL);
        errorCodeMapping.put(FACE_CHECK_FAIL_2, WalletResultCode.FACE_CHECK_FAIL);
        errorCodeMapping.put(LOAN_CUR_TERM_NOT_SUPPORT, WalletResultCode.LOAN_CUR_TERM_NOT_SUPPORT);
        errorCodeMapping.put(LOAN_AMOUNT_TOO_SMALL, WalletResultCode.LOAN_AMOUNT_TOO_SMALL);
        errorCodeMapping.put(BANKCARD_CHECK_FAIL_1, WalletResultCode.BANKCARD_CHECK_FAIL);
        errorCodeMapping.put(BANKCARD_CHECK_FAIL_2, WalletResultCode.BANKCARD_CHECK_FAIL);
        errorCodeMapping.put(CUR_ORDER_NOT_SUPPORT_COUPON_1, WalletResultCode.CUR_ORDER_NOT_SUPPORT_COUPON);
        errorCodeMapping.put(ORDER_EXPIRE, WalletResultCode.ORDER_EXPIRE);
        errorCodeMapping.put(INVALID_ORDER_NUMBER, WalletResultCode.INVALID_ORDER_NUMBER);
        errorCodeMapping.put(SEND_MESSAGE_FAIL, WalletResultCode.SEND_MESSAGE_FAIL);
        errorCodeMapping.put(SEND_MESSAGE_FREQUENT, WalletResultCode.SEND_MESSAGE_FREQUENT);
        errorCodeMapping.put(VERIFY_MESSAGE_FREQUENT, WalletResultCode.VERIFY_MESSAGE_FREQUENT);
        errorCodeMapping.put(VERIFY_CODE_EXPIRE, WalletResultCode.VERIFY_CODE_EXPIRE);
        errorCodeMapping.put(VERIFY_CODE_ERROR, WalletResultCode.VERIFY_CODE_ERROR);
        errorCodeMapping.put(INVALID_OPERATION, WalletResultCode.INVALID_OPERATION);
        errorCodeMapping.put(NEW_LOCK_UP_INTERCEPTED, WalletResultCode.NEW_LOCK_UP_INTERCEPTED);
        errorCodeMapping.put(PAY_FAIL, WalletResultCode.PAY_FAIL);
        errorCodeMapping.put(PAY_FAIL_1, WalletResultCode.PAY_FAIL);
        errorCodeMapping.put(PAY_FAIL_2, WalletResultCode.PAY_FAIL);
        errorCodeMapping.put(PAY_FAIL_3, WalletResultCode.PAY_FAIL);
        errorCodeMapping.put(INSUFFICIENT_BALANCE, WalletResultCode.INSUFFICIENT_BALANCE);



        errorCodeMapping.put(USER_PROCESS_STATUS_ABNORMAL_LOAN, WalletResultCode.USER_PROCESS_STATUS_ABNORMAL);
        errorCodeMapping.put(ORDER_INVALID_LOAN, WalletResultCode.ORDER_INVALID);

        errorCodeMapping.put(ALREADY_PAYOFF_NOT_REPEAT_REPAY, WalletResultCode.THE_LOAN_IS_SETTLED);
        errorCodeMapping.put(REPEAT_REPAY, WalletResultCode.LOAN_SETTLED_PLEASE_NOT_REPAY);
        errorCodeMapping.put(LOCK_NOT_PART_REPAY_CUR, WalletResultCode.LOCK_NOT_PART_REPAY_CUR);
        errorCodeMapping.put(LOCK_NOT_PART_REPAY_FEATURE, WalletResultCode.LOCK_NOT_PART_REPAY_FEATURE);
        errorCodeMapping.put(NOT_FOUND_RELATION_TRANSACTION, WalletResultCode.NOT_FOUND_RELATION_TRANSACTION);
        errorCodeMapping.put(DISCOUNT_AMOUNT_NOT_MATCH_ACTUAL_AMOUNT, WalletResultCode.DISCOUNT_AMOUNT_NOT_MATCH_ACTUAL_AMOUNT);
        errorCodeMapping.put(REPAY_AMOUNT_TOO_BIG, WalletResultCode.REPAY_AMOUNT_TOO_BIG);
        errorCodeMapping.put(EXIST_NOT_FINISH_REPAY_ORDER, WalletResultCode.EXIST_NOT_FINISH_REPAY_ORDER);
        errorCodeMapping.put(LOCK_PART_INTERCEPT, WalletResultCode.LOCK_PART_INTERCEPT);
        errorCodeMapping.put(COUPON_NOT_COMPLY_RULE, WalletResultCode.COUPON_NOT_COMPLY_RULE);
        errorCodeMapping.put(COUPON_USED, WalletResultCode.COUPON_USED_REPAY);
        errorCodeMapping.put(COUPON_EXPIRED, WalletResultCode.COUPON_EXPIRED);
        errorCodeMapping.put(COUPON_NOT_EXIST, WalletResultCode.COUPON_NOT_EXIST_REPAY);
        errorCodeMapping.put(SETTLEMENT_SEND_TOO_FREQUENT,WalletResultCode.SETTLEMENT_SEND_TOO_FREQUENT);
        errorCodeMapping.put(USER_NOT_EXIST, WalletResultCode.USER_NOT_EXIST_1);
        errorCodeMapping.put(USER_LOG_OFF, WalletResultCode.USER_CANCELLED);
    }

    /**
     * 参数转换
     *
     * @param dto dto
     * @return
     * @throws BusinessException
     */
    public void convertErrorCode(GeneralBaseDto dto) {
        if (!Objects.equals(dto.getCode(), 0)) {
            MdcUtil.putSourceFailCodeAndReason(dto.getCode(), dto.getDesc());
            WalletResultCode code = WalletResultCode.getWalletResultCode(generalErrorCodeMappingConfig.getGeneralMapping().get(dto.getCode()));
            if (Objects.isNull(code)) {
                throw new BusinessException(WalletResultCode.LOAN_SERVICE_ERROR);
            }
            LogUtil.runInfoLog("error code {}", dto.getCode());
            throw new BusinessException(code);
        }
    }
}
