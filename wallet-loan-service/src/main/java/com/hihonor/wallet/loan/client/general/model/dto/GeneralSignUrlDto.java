package com.hihonor.wallet.loan.client.general.model.dto;

import lombok.Data;

@Data
public class GeneralSignUrlDto {
    /**
     * 签约状态
     * 0-不支持
     * 1-未签约
     * 2-已签约
     * 3-已解约
     */
    private Integer signStatus;

    /**
     * 支付宝代扣签约链接
     * （未签约和已解约时必传）
     */
    private String signUrl;

    /**
     * 支付宝代扣签约链接
     * （未签约和已解约时必传）
     */
    private String signUrlValidTime;

    /**
     * 协议地址（未签约和已解约时必传）
     */
    private String contractUrl;

    /**
     * 协议名称（未签约和已解约时必传）
     */
    private String contractName;
}
