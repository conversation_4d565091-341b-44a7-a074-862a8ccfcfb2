package com.hihonor.wallet.loan.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hihonor.wallet.common.model.dto.transcard.PageBaseResponse;
import com.hihonor.wallet.loan.entity.LoanAdActivityEntity;
import com.hihonor.wallet.common.model.dto.loan.LoanAdActivityDto;
import com.hihonor.wallet.common.model.param.loan.LoanAdActivityListParam;
import com.hihonor.wallet.common.model.param.loan.LoanAdActivityParam;
import com.hihonor.wallet.loan.model.dto.LoanAdActivityQueryDto;
import com.hihonor.wallet.loan.model.param.LoanActivityQueryParam;

/**
 * 实名认证运营页
 * <AUTHOR>
 */
public interface LoanAdActivityService extends IService<LoanAdActivityEntity> {


    /**
     * 获取实名认证页配置
     *
     * @param param 查询参数
     * @return 实名认证页配置
     */
    LoanAdActivityQueryDto getLoanAdActivityConfig(LoanActivityQueryParam param);
}
