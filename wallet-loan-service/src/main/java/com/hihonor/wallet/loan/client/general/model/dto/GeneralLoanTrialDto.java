/*
 * Copyright (c) Honor Device Co., Ltd. 2024-2024. All rights reserved.
 */

package com.hihonor.wallet.loan.client.general.model.dto;

import java.util.List;

import com.hihonor.wallet.loan.model.dto.ContractDto;
import com.hihonor.wallet.loan.model.dto.RepayPlanTermDto;

import lombok.Data;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-02-27
 */
@Data
public class GeneralLoanTrialDto {
    /**
     * 应还总额
     */
    private Long shouldRepayAmount;

    /**
     * 应还本金总额
     */
    private Long shouldRepayPrinAmount;

    /**
     * 应还利息总额
     */
    private Long shouldRepayInterAmount;

    /**
     * 应还服务费总额
     */
    private Long shouldRepayServiceFee;

    /**
     * 首次还款日
     */
    private String firstRepayDate;

    /**
     * 最后还款日
     */
    private String lastRepayDate;

    /**
     * 优惠券减免金额
     */
    private Long reductionAmount;

    /**
     * 出资方
     */
    private String stakeholders;
    /**
     * 综合年利率
     */
    private String annualRate;

    /**
     * 优惠前原价年利率，示例12.95【即12.95%】
     */
    private String originalRate;

    /**
     * 还款计划
     */
    private List<RepayPlanTermDto> repayPlanTerms;

    /**
     * 协议列表
     */
    private List<ContractDto> contract;

    /**
     * 限时降价优惠金额，单位：分
     */
    private Long tempDiscount;

    /**
     * 按期还优惠金额，单位：分
     */
    private Long scheduleDiscount;

    /**
     * 总优惠金额，单位：分
     */
    private Long totalDiscount;
}
