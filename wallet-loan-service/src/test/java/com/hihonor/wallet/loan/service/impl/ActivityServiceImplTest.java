package com.hihonor.wallet.loan.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hihonor.wallet.WalletLoanApplication;
import com.hihonor.wallet.common.exception.BusinessException;
import com.hihonor.wallet.common.model.BaseRequest;
import com.hihonor.wallet.common.util.RequestUtils;
import com.hihonor.wallet.loan.client.LoanClient;
import com.hihonor.wallet.loan.client.model.dto.GetMarkingActivityUrlDto;
import com.hihonor.wallet.loan.client.model.param.GetMarkingActivityUrlParam;
import com.hihonor.wallet.loan.constant.LoanConstant;
import com.hihonor.wallet.loan.entity.CreditApplyEntity;
import com.hihonor.wallet.loan.entity.LoanUserEntity;
import com.hihonor.wallet.loan.mapper.LoanUserMapper;

import com.hihonor.wallet.loan.model.dto.GetMarkingActivityUrlResponse;
import com.hihonor.wallet.loan.service.CreditService;

import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.sql.Date;
import java.time.LocalDateTime;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@SpringBootTest(classes = WalletLoanApplication.class)
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@Slf4j
public class ActivityServiceImplTest {

     @MockBean
    private LoanUserMapper loanUserMapper;

     @MockBean
    private CreditService creditService;

     @MockBean
    private LoanClient loanClient;

    @Autowired
    private ActivityServiceImpl activityService;

    MockedStatic<RequestUtils> requestUtils;

    @Before
    public void setUp() {
//        MockitoAnnotations.initMocks(this);
        requestUtils = mockStatic(RequestUtils.class);
        requestUtils.when(() -> RequestUtils.getUid()).thenReturn("6160086500000119599");
    }

    @After
    public void tearDown() {
        requestUtils.close();
    }

    @Test
    public void getMarkingActivityUrlUserNotFound() {
        when(loanUserMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        BaseRequest request = new BaseRequest();
        GetMarkingActivityUrlResponse response = activityService.getMarkingActivityUrl(request);

        assertNull(response.getMarkingActivityUrl());
        log.info("response --- {}",response);
    }

    @Test
    public void getMarkingActivityUrlCreditApplicationNotFound() {
        LoanUserEntity loanUserEntity = new LoanUserEntity();
        loanUserEntity.setUserId(1L);
        loanUserEntity.setAccessResult(LoanConstant.UserAccessResult.APPROVE);
        loanUserEntity.setInUse(LoanConstant.InUse.IN_USE);
        loanUserEntity.setCreateTime(Date.valueOf(Date.valueOf(LocalDateTime.now().toLocalDate()).toLocalDate()));

        when(loanUserMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(loanUserEntity);
        when(creditService.getCreditApplyRecord(anyLong(), anyString(), anyInt())).thenReturn(null);

        BaseRequest request = new BaseRequest();
        GetMarkingActivityUrlResponse response = activityService.getMarkingActivityUrl(request);

        assertNull(response.getMarkingActivityUrl());
        log.info("response --- {}",response);
    }

    @Test
    public void getMarkingActivityUrlNotApproved() {
        LoanUserEntity loanUserEntity = new LoanUserEntity();
        loanUserEntity.setUserId(1L);
        loanUserEntity.setAccessResult(LoanConstant.UserAccessResult.APPROVE);
        loanUserEntity.setInUse(LoanConstant.InUse.IN_USE);
        loanUserEntity.setCreateTime(Date.valueOf(LocalDateTime.now().toLocalDate()));

        CreditApplyEntity creditApplyEntity = new CreditApplyEntity();
        creditApplyEntity.setApplyStatus(LoanConstant.CreditStatus.REJECT);

        when(loanUserMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(loanUserEntity);
        when(creditService.getCreditApplyRecord(anyLong(), anyString(),anyInt())).thenReturn(creditApplyEntity);

        BaseRequest request = new BaseRequest();
        GetMarkingActivityUrlResponse response = activityService.getMarkingActivityUrl(request);

//        assertEquals(LoanConstant.CreditStatus.REJECTED, response.getCreditStatus());
        assertNull(response.getMarkingActivityUrl());
        log.info("response --- {}",response);
    }

    @Test
    public void getMarkingActivityUrlSuccess() throws Exception {
        LoanUserEntity loanUserEntity = new LoanUserEntity();
        loanUserEntity.setUserId(1L);
        loanUserEntity.setAccessResult(LoanConstant.UserAccessResult.APPROVE);
        loanUserEntity.setInUse(LoanConstant.InUse.IN_USE);
        loanUserEntity.setCreateTime(Date.valueOf(LocalDateTime.now().toLocalDate()));
        loanUserEntity.setSupplier(1);
        loanUserEntity.setOutOpenId("outOpenId");
        loanUserEntity.setOpenId("openId");

        CreditApplyEntity creditApplyEntity = new CreditApplyEntity();
        creditApplyEntity.setApplyStatus(LoanConstant.CreditStatus.APPROVED);

        GetMarkingActivityUrlDto getMarkingActivityUrlDto = new GetMarkingActivityUrlDto();
        getMarkingActivityUrlDto.setUrl("http://example.com/activity");
        getMarkingActivityUrlDto.setValidateTime(String.valueOf(Date.valueOf(LocalDateTime.now().toLocalDate())));

        when(loanUserMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(loanUserEntity);
        when(creditService.getCreditApplyRecord(anyLong(), anyString(),anyInt())).thenReturn(creditApplyEntity);
        when(loanClient.getMarkingActivityUrl(any(GetMarkingActivityUrlParam.class))).thenReturn(getMarkingActivityUrlDto);

        BaseRequest request = new BaseRequest();
        GetMarkingActivityUrlResponse response = activityService.getMarkingActivityUrl(request);

        assertNotNull(response);
        log.info("response --- {}",response);
    }

    @Test
    public void getMarkingActivityUrlException() throws Exception {
        LoanUserEntity loanUserEntity = new LoanUserEntity();
        loanUserEntity.setUserId(1L);
        loanUserEntity.setAccessResult(LoanConstant.UserAccessResult.APPROVE);
        loanUserEntity.setInUse(LoanConstant.InUse.IN_USE);
        loanUserEntity.setCreateTime(Date.valueOf(LocalDateTime.now().toLocalDate()));
        loanUserEntity.setSupplier(1);
        loanUserEntity.setOutOpenId("outOpenId");
        loanUserEntity.setOpenId("openId");

        CreditApplyEntity creditApplyEntity = new CreditApplyEntity();
        creditApplyEntity.setApplyStatus(LoanConstant.CreditStatus.APPROVED);

        when(loanUserMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(loanUserEntity);
        when(creditService.getCreditApplyRecord(anyLong(), anyString(), anyInt())).thenReturn(creditApplyEntity);
        when(loanClient.getMarkingActivityUrl(any(GetMarkingActivityUrlParam.class))).thenThrow(new BusinessException("Failed to get URL"));

        BaseRequest request = new BaseRequest();
        GetMarkingActivityUrlResponse response = activityService.getMarkingActivityUrl(request);

//        assertEquals(LoanConstant.CreditStatus.ORIGINAL, response.getCreditStatus());
        assertNull(response.getMarkingActivityUrl());
        log.info("response --- {}",response);
    }
}
