package com.hihonor.wallet.loan.service.impl;

import com.hihonor.wallet.WalletLoanApplication;
import com.hihonor.wallet.common.model.BaseHeader;
import com.hihonor.wallet.common.util.IdUtils;
import com.hihonor.wallet.common.util.RequestUtils;
import com.hihonor.wallet.loan.config.LoanEntryConfig;
import com.hihonor.wallet.loan.model.dto.YoyoEntryDto;
import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;


@SpringBootTest(classes = WalletLoanApplication.class)
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@Slf4j
public class YoyoEntryServiceImplTest {

    @InjectMocks
    private YoyoEntryServiceImpl service;

    @Mock
    private LoanEntryConfig loanEntryConfig;

    MockedStatic<RequestUtils> requestUtils;

    private static final String userId = "855008123456119796";
    private static final Long userIdLong = 855008123456119796L;
    private static final Long userIdLong2 = 855008123456119797L;
    private static final String openId = "123456789987111";
    private static final String mobile = "123456789987111";
    private static final String outOrderNo = "123456789123";
    private static final String outOpenId = "442ba8a6b5725f1b76ad5a2c326717cc36cb3fa7";
    private static final String applyNo = IdUtils.generateCreditOrderId(userId, 1);

    @Before
    public void setUp() {
        BaseHeader header = new BaseHeader();
        requestUtils = mockStatic(RequestUtils.class);
        header.setPackageName("com.hihonor.health");
        header.setDeviceModel("PGT-AN10");
        header.setCid("1");
        header.setHnWalletEnable(false);
        header.setVersionCode(1);
        header.setSubCid("1");
        header.setLoc("1");
        header.setCid("");
        requestUtils.when(() -> RequestUtils.getUid()).thenReturn("6160086500000119599");
        requestUtils.when(() -> RequestUtils.getSupplier()).thenReturn("1");
        requestUtils.when(() -> RequestUtils.getMobileNo()).thenReturn(mobile);
        requestUtils.when(() -> RequestUtils.getBaseHeader()).thenReturn(header);
    }

    @After
    public void tearDown() {
        requestUtils.close();
    }

    @Test
    public void getYoyoEntryYoyoEnabledVersionCodeGreaterThanOrEqualToHnidVersion() {
        // Mock数据
        when(loanEntryConfig.isYoyoEnabled()).thenReturn(true);
        when(loanEntryConfig.getHnidVersion()).thenReturn(100L);

        // 执行测试
        YoyoEntryDto dto = service.getYoyoEntry();

        // 断言
        assertFalse(dto.isYoyo());
    }

    @Test
    public void getYoyoEntryYoyoEnabledVersionCodeLessThanHnidVersion() {
        // Mock数据
        when(loanEntryConfig.isYoyoEnabled()).thenReturn(true);
        when(loanEntryConfig.getHnidVersion()).thenReturn(100L);
        
        // 执行测试
        YoyoEntryDto dto = service.getYoyoEntry();

        // 断言
        assertFalse(dto.isYoyo());
    }

    @Test
    public void getYoyoEntryYoyoDisabledVersionCodeGreaterThanOrEqualToHnidVersion() {
        // Mock数据
        when(loanEntryConfig.isYoyoEnabled()).thenReturn(false);
        when(loanEntryConfig.getHnidVersion()).thenReturn(100L);

        // 执行测试
        YoyoEntryDto dto = service.getYoyoEntry();

        // 断言
        assertFalse(dto.isYoyo());
    }

    @Test
    public void getYoyoEntryYoyoDisabledVersionCodeLessThanHnidVersion() {
        // Mock数据
        when(loanEntryConfig.isYoyoEnabled()).thenReturn(false);
        when(loanEntryConfig.getHnidVersion()).thenReturn(100L);
        // 执行测试
        YoyoEntryDto dto = service.getYoyoEntry();

        // 断言
        assertFalse(dto.isYoyo());
    }
}
