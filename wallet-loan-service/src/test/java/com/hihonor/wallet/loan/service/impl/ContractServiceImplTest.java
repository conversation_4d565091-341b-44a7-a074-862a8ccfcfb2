package com.hihonor.wallet.loan.service.impl;

import com.hihonor.wallet.WalletLoanApplication;
import com.hihonor.wallet.common.redis.RedisUtil;
import com.hihonor.wallet.loan.client.LoanClient;
import com.hihonor.wallet.loan.constant.LoanConstant;
import com.hihonor.wallet.loan.model.dto.ContractDetailDto;
import com.hihonor.wallet.loan.model.dto.ContractInfoDto;
import com.hihonor.wallet.loan.model.dto.ContractInfoRecordDto;
import com.hihonor.wallet.loan.model.dto.LoanSupplierDto;
import com.hihonor.wallet.loan.model.param.ContractDetailParam;
import com.hihonor.wallet.loan.model.param.ContractListParam;
import com.hihonor.wallet.loan.model.param.PreLoanDataParam;
import com.hihonor.wallet.loan.service.LoanSupplierService;
import com.hihonor.wallet.loan.service.UserService;
import groovy.util.logging.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;

@SpringBootTest(classes = WalletLoanApplication.class)
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@Slf4j
public class ContractServiceImplTest {

    @Autowired
    private ContractServiceImpl contractServiceImpl;

    @MockBean
    private LoanClient loanClient;

    @MockBean
    private UserService userService;

    @MockBean
    private LoanSupplierService loanSupplierService;

    @MockBean
    private RedisUtil redisUtil;

    @Before
    public void setup() {
        // 初始化Mock对象
    }

    @Test
    public void getContractList() {
        // 准备测试数据
        ContractListParam param = new ContractListParam();
        param.setType(LoanConstant.Contract.LOAN);
        param.setPreLoanData(new PreLoanDataParam());

        // 模拟loanClient的行为
        List<ContractInfoDto> contractInfoDtos = new ArrayList<>();
        when(loanClient.queryAgreement(any())).thenReturn(contractInfoDtos);

        // 执行测试方法
        List<ContractInfoDto> result = contractServiceImpl.getContractList(param);

        // 断言结果
        assertEquals(contractInfoDtos, result);
    }

    @Test
    public void getContractDetail() {
        // 准备测试数据
        ContractDetailParam param = new ContractDetailParam();
        param.setType(LoanConstant.ProtocolType.PROTOCOL_TYPE_URL);

        // 模拟loanClient的行为
        List<ContractDetailDto> contractDetailDtos = new ArrayList<>();
        when(loanClient.queryContractDetail(any())).thenReturn(contractDetailDtos);

        // 执行测试方法
        List<ContractDetailDto> result = contractServiceImpl.getContractDetail(param);

        // 断言结果
        assertEquals(contractDetailDtos, result);
    }

    @Test
    public void getDomainWhitelist() {
        // 准备测试数据
        String key = LoanConstant.LOAN_DOMAIN_WHITELIST;
        Set<String> domainWhitelist = new java.util.HashSet<>();

        // 模拟redisUtil的行为
        when(redisUtil.getSetMembers(key)).thenReturn(Collections.singleton(domainWhitelist));

        // 执行测试方法
        List<String> result = contractServiceImpl.getDomainWhitelist();

        // 断言结果
        assertEquals(new ArrayList<>(domainWhitelist), result);
    }

    @Test
    public void getContractListV2() {
        // 准备测试数据
        ContractListParam param = new ContractListParam();
        param.setType(LoanConstant.Contract.LOAN);
        param.setPreLoanData(new PreLoanDataParam());

        // 模拟loanClient的行为
        List<ContractInfoDto> contractInfoDtos = new ArrayList<>();
        when(loanClient.queryAgreement(any())).thenReturn(contractInfoDtos);

        // 模拟loanSupplierService的行为
        LoanSupplierDto cpConfig = new LoanSupplierDto();
        when(loanSupplierService.getCpConfigBySupplierId(anyInt())).thenReturn(cpConfig);

        // 执行测试方法
        ContractInfoRecordDto result = contractServiceImpl.getContractListV2(param);

        // 断言结果
        assertNotNull(result);
        assertEquals(contractInfoDtos, result.getContractList());
    }
}
