package com.hihonor.wallet.loan.service.impl;

import com.hihonor.wallet.WalletLoanApplication;
import com.hihonor.wallet.common.model.dto.configmgr.AdActivityDto;
import com.hihonor.wallet.common.model.dto.configmgr.AdSpaceDto;
import com.hihonor.wallet.common.model.param.configmgr.LoanBannerAdActivityParam;
import com.hihonor.wallet.loan.client.LoanClient;
import com.hihonor.wallet.loan.client.model.dto.QueryUserCreditInfoDto;
import com.hihonor.wallet.loan.entity.AdBannerActivityEntity;
import com.hihonor.wallet.loan.entity.AdBannerSpaceEntity;
import com.hihonor.wallet.loan.entity.LoanUserEntity;
import com.hihonor.wallet.loan.mapper.AdBannerActivityMapper;
import com.hihonor.wallet.loan.mapper.AdBannerSpaceMapper;
import com.hihonor.wallet.loan.mapper.LoanUserMapper;
import com.hihonor.wallet.loan.model.dto.AdBannerActivityConfigDto;
import com.hihonor.wallet.loan.model.dto.BandCardCouponDto;
import com.hihonor.wallet.loan.service.impl.LoanInnerServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;


@SpringBootTest(classes = WalletLoanApplication.class)
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@Slf4j
public class LoanInnerServiceImplTest {

    @Autowired
    private LoanInnerServiceImpl loanInnerServiceImpl;

   @MockBean
    private AdBannerSpaceMapper adBannerSpaceMapper;

   @MockBean
    private AdBannerActivityMapper adBannerActivityMapper;

   @MockBean
    private LoanUserMapper loanUserMapper;

   @MockBean
    private LoanClient loanClient;

    @Before
    public void setup() {
        // 初始化Mock对象
    }

    @Test
    public void getLoanAdSpaceList() {
        // 准备测试数据
        List<AdBannerSpaceEntity> entities = new ArrayList<>();
        AdBannerSpaceEntity entity = new AdBannerSpaceEntity();
        entity.setSpaceCode("spaceCode");
        entity.setParentId(0);
        entities.add(entity);

        // 模拟adBannerSpaceMapper的行为
        when(adBannerSpaceMapper.selectList(any())).thenReturn(entities);

        // 执行测试方法
        List<AdSpaceDto> result = loanInnerServiceImpl.getLoanAdSpaceList();

        // 断言结果
        assertNotNull(result);
        assertEquals(1, result.size());
    }

    @Test
    public void getLoanAdActivityList() {
        // 准备测试数据
        String spaceCode = "spaceCode";
        List<AdBannerActivityEntity> entities = new ArrayList<>();
        AdBannerActivityEntity entity = new AdBannerActivityEntity();
        entity.setSpaceCode(spaceCode);
        entities.add(entity);

        // 模拟adBannerActivityMapper的行为
        when(adBannerActivityMapper.selectList(any())).thenReturn(entities);

        // 执行测试方法
        List<AdActivityDto> result = loanInnerServiceImpl.getLoanAdActivityList(spaceCode);

        // 断言结果
        assertNotNull(result);
        assertEquals(1, result.size());
    }

    @Test
    public void testSaveLoanBannerAdActivity() {
        // 准备测试数据
        LoanBannerAdActivityParam adActivityParam = new LoanBannerAdActivityParam();
        adActivityParam.setSpaceCode("spaceCode");
        adActivityParam.setActivityName("activityName");

        // 模拟adBannerActivityMapper的行为
        when(adBannerActivityMapper.selectOne(any())).thenReturn(null);

        // 执行测试方法
        loanInnerServiceImpl.saveLoanBannerAdActivity(adActivityParam);

        // 断言结果
        verify(adBannerActivityMapper, times(1)).insert(any());
    }

    @Test
    public void getAdBannerActivityList() {
        // 准备测试数据
        List<String> spaceCodes = new ArrayList<>();
        spaceCodes.add("spaceCode");

        // 模拟adBannerActivityMapper的行为
        when(adBannerActivityMapper.selectList(any())).thenReturn(new ArrayList<>());

        // 执行测试方法
        Map<String, List<AdBannerActivityConfigDto>> result = loanInnerServiceImpl.getAdBannerActivityList(spaceCodes);
        log.info("result --- {}",result);

        // 断言结果
        assertNotNull(result);
    }

    @Test
    public void getAdActivityListForGuest() {
        // 准备测试数据
        List<String> spaceCodes = new ArrayList<>();
        spaceCodes.add("spaceCode");

        // 模拟adBannerActivityMapper的行为
        when(adBannerActivityMapper.selectList(any())).thenReturn(new ArrayList<>());

        // 执行测试方法
        Map<String, List<AdBannerActivityConfigDto>> result = loanInnerServiceImpl.getAdActivityListForGuest(spaceCodes);
        log.info("result --- {}",result);

        // 断言结果
        assertNotNull(result);

    }

    @Test
    public void getCreditInfo() {
        // 准备测试数据
        Long userId = 1L;

        // 模拟loanUserMapper的行为
        when(loanUserMapper.selectOne(any())).thenReturn(new LoanUserEntity());

        // 执行测试方法
        QueryUserCreditInfoDto result = loanInnerServiceImpl.getCreditInfo(userId);
        log.info("result --- {}",result);

        // 断言结果
        assertNull(result);
    }

    @Test
    public void couponsByUserId() {
        // 准备测试数据
        Long userId = 1L;

        // 模拟loanUserMapper的行为
        when(loanUserMapper.selectOne(any())).thenReturn(new LoanUserEntity());

        // 执行测试方法
        List<BandCardCouponDto> result = loanInnerServiceImpl.couponsByUserId(userId);
        log.info("result --- {}",result);

        // 断言结果
        assertNotNull(result);
    }
}
