import "./style.css";
import "/public/icon/style.css";
import "/public/notFoundIcon/style.css";
import "/public/credit/style.css";
import { createApp } from "./main";
import { sha1Hex, storeMarketingTracking } from "./helpers/utils";
import { report } from "./helpers/native-bridge";
import {
  storePopupConfigList,
  delRetentionStatusStore,
  delPopupConfigList,
} from "./helpers/configUtils";
import { setCurrentStore, setAxiosInstance, getCurrentStore } from "./helpers/constants";
import httpClient from "./helpers/http-client";
import { getStore, setWithExactExpire } from "./helpers/storage";

setAxiosInstance(httpClient);
if (document.cookie && document.cookie.match(/\saccess_fail=1/)) {
  const date = new Date();
  date.setTime(date.getTime() + 3 * 1000);
  document.cookie = `access_fail=0; expires=${date.toUTCString()}; path=/;`;
  import("./helpers/native-bridge").then((module) => {
    module.refreshToken().then(() => {
      if (window.location.pathname !== "/wallet-loan-web/pages/notfound") {
        setTimeout(() => {
          window.location.reload();
        }, 500);
      }
    });
  });
}

async function initApm(userId, app) {
  // eslint-disable-next-line no-underscore-dangle
  const { url, dsn, projectId, envType, token } = window.__apmSettings;
  return new Promise((resovle, reject) => {
    const scriptNode = document.createElement("script");
    scriptNode.src = url;
    scriptNode.onload = () => {
      if (Object.prototype.hasOwnProperty.call(window, "Sentry")) {
        const environment = envType === "production" ? "production" : "development";
        window.Sentry.init({
          app,
          dsn,
          projectId,
          businessId: "", // 选填，如果注册应用有businessId即写入
          environment,
          token,
          integrations: [
            window.Sentry.browserTracingIntegration(),
            window.Sentry.httpClientIntegration({
              failedRequestStatusCodes: [[200, 599]],
              failedRequestBusinessCodes: [[9000, 9123]],
            }),
          ],
        })
          .then(() => {
            resovle();
          })
          .catch(() => {
            reject();
          });
        window.Sentry.setParams({
          // 用原始用户Id作标识
          userId: `userId-${userId}`,
          sessionId: `${new Date().getTime()}-${window.crypto.randomUUID().substring(2, 8)}`,
        });
      }
    };
    scriptNode.onerror = () => {
      reject();
    };
    document.head.appendChild(scriptNode);
  });
}

const base = "/wallet-loan-web/pages";
createApp(window.location.pathname.replace(base, "")).then((res) => {
  document.querySelector("#server-data").remove();
  const { app, store } = res;
  // eslint-disable-next-line no-underscore-dangle
  store.$state = window.__INITIAL_STATE__;
  // 处理数据
  const { userId } = store.$state.param;
  store.$state.param.userId = store.$state.param.userId
    ? sha1Hex(store.$state.param.userId)
    : store.$state.param.userId;
  store.param = store.$state.param;
  setCurrentStore(store);
  // eslint-disable-next-line no-underscore-dangle
  delete window.__INITIAL_STATE__;

  storeMarketingTracking(store.$state.param.marketing_tracking);
  if (!store.$state.param.urlref) {
    delPopupConfigList();
    delRetentionStatusStore();
    storePopupConfigList(store.$state.param.adSpace);
  }

  const pageNameMap = {
    "/wallet-loan-web/pages/credit/realname": "credit_realname_page",
    "/wallet-loan-web/pages/credit/sign": "credit_agreement_page",
    "/wallet-loan-web/pages/credit/infoform": "credit_info_page",
    "/wallet-loan-web/pages/loan/calc": "loan_trial_page",
    "/wallet-loan-web/pages/loan/sign": "loan_agreement_page",
    "/wallet-loan-web/pages/loan/sms": "loan_sms_page",
  };
  const pageName = pageNameMap[window.location.pathname];
  if (pageName) {
    const event = { page_name: pageName };
    if (pageName === "credit_realname_page") {
      event.isLogin = !!store.$state.param.userId;
      event.isRealName = store.$state.realNameInfo?.status === 1;
    }
    report("wallet_page_view", event);
  }
  initApm(userId, app).finally(() => {
    app.mount("#app");
  });
});

document.addEventListener("touch", (e) => {
  const clickName = e.target.getAttribute("data-click-name");
  if (clickName) {
    report("wallet_page_click", { click_name: clickName });
  }
});

document.addEventListener("click", (e) => {
  const clickName = e.target.getAttribute("data-click-name");
  if (clickName) {
    report("wallet_page_click", { click_name: clickName });
  }
});

let pageHidden = document.hidden;
document.addEventListener("visibilitychange", () => {
  if (document.hidden) {
    pageHidden = true;
  }
});

let resLoadError = false;
window.addEventListener("load", () => {
  const perfData = window.performance.getEntriesByType("navigation")[0];
  const paintData = (window.performance.getEntriesByType("paint") || []).find(
    (entry) => entry.name === "first-contentful-paint",
  );
  const fpData = (window.performance.getEntriesByType("paint") || []).find(
    (entry) => entry.name === "first-paint",
  );
  if (
    paintData?.startTime > 300000 ||
    (paintData && fpData && paintData.startTime - fpData.startTime > 1000)
  ) {
    pageHidden = true;
  }
  if (perfData) {
    report(
      "wallet_client_performance",
      {
        performance_name: "page_performance",
        related_page: window.location.pathname,
        redirect_cost: perfData.redirectEnd.toFixed(2),
        dns_cost: perfData.domainLookupEnd.toFixed(2),
        dowload_cost: perfData.responseEnd.toFixed(2),
        total_cost: perfData.domInteractive.toFixed(2),
        fcp_time: paintData?.startTime.toFixed(2),
        networkType: navigator?.connection?.effectiveType,
        downlink: navigator?.connection?.downlink,
        pageHidden,
        resLoadError,
      },
      -1,
    );
  }
});

window.addEventListener(
  "error",
  (event) => {
    const { target } = event;
    if (
      target instanceof HTMLElement &&
      ["LINK", "SCRIPT", "IMG"].indexOf(target.nodeName) !== -1
    ) {
      const src = target.src || target.href;
      if (window.location.href.indexOf(src) !== 0) {
        resLoadError = ["LINK", "SCRIPT"].includes(target.nodeName);
        setTimeout(() => {
          report(
            "wallet_page_result",
            {
              performance_name: "network_error",
              related_page: window.location.pathname,
              network_type: navigator?.connection?.effectiveType,
              downlink: navigator?.connection?.downlink,
              res_url: src,
            },
            -1,
          );
        }, 0);
      }
    }
  },
  true,
);

Object.keys(localStorage).forEach((key) => {
  try {
    setTimeout(() => {
      const value = JSON.parse(localStorage.getItem(key));
      if (value.expireTime) {
        const now = new Date();
        now.setDate(now.getDate() - 2);
        if (now.getTime() > value.expireTime) {
          localStorage.removeItem(key);
        } else {
          /**
           * 根据当前用户的ID和缓存数据的有效期，更新贷款存储信息。
           * 如果用户ID是整数，则对其进行SHA1哈希处理。
           * 当指定键对应的缓存数据存在且未过期（有效期至少为24小时）时，重新设置其过期时间为24小时。
           *
           * @param {string} key - 缓存项的键名，格式为`U_LOAN_PROCESS_${userId}`
           * @param {Object} value - 缓存项的值对象，包含expireTime属性表示过期时间戳
           * @param {Date} now - 当前时间对象
           */
          let { userId } = getCurrentStore().param;
          if (/\d+/.test(userId)) {
            userId = sha1Hex(userId);
          }
          if (
            key === `U_LOAN_PROCESS_${userId}` &&
            value.expireTime - new Date().getTime() >= 86400000
          ) {
            const loanStorage = getStore(key);
            setWithExactExpire(key, loanStorage, "24H");
          }
        }
      }
    }, 0);
  } catch (e) {
    /* empty */
  }
});
