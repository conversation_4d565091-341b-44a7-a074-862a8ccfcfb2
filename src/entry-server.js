import { AsyncLocalStorage } from "async_hooks";
import { renderToString } from "vue/server-renderer";
import { createApp } from "./main";
import bffIntls from "./bff";
import redirectIntls from "./redirect";
import { request, buildContextStore } from "./helpers/utils";
import httpClient from "./helpers/http-server";
import { setAxiosInstance } from "./helpers/constants";
import { logInterface, logRun } from "./helpers/logger/log-util";
import { getAdActivityListV2 } from "./api/ad";

global.asyncLocalStorage = new AsyncLocalStorage();
setAxiosInstance(httpClient);

export async function render(...args) {
  const [url, ssrManifest] = args;
  const { req, context } = global.asyncLocalStorage.getStore();
  const { country, language, versionCode } = context.deviceInfo;
  const { sdkVersionCode } = context;
  const xoaid = context["x-oaid"];
  const param = {
    ...req.query,
    ...(context?.userInfo.userId && { userId: context.userInfo.userId }),
    country,
    language,
    versionCode,
    sdkVersionCode,
    xoaid,
    isLogin: req?.cookies?.isLogin === "true",
    skipLiveDetection: req?.cookies?.skipLiveDetection === "true",
  };

  const { app, store, file } = await createApp(url, param);
  const outsideDomains =
    process.env.DEF_ENV_TYPE === "production"
      ? [
          "https://contentplatform-drcn.hihonorcdn.com/",
          "https://dapm.yun.honor.com/",
          "https://dapm-sdk.yun.honor.com/",
        ]
      : [
          "https://contentplatform-drcn.hihonorcdn.com/",
          "https://test-content-platform.obs.cn-north1.ctyun.cn/",
          "https://uem-uat.test.hihonor.com/",
          "https://dapm-sdk-1311258067.cos.ap-beijing.myqcloud.com/",
        ];

  let head = outsideDomains.map((item) => `<link rel="dns-prefetch" href="${item}" />`).join("");
  if (file && ssrManifest) {
    file.split(",").forEach((item) => {
      if (ssrManifest[`src${item}`]) {
        head += ssrManifest[`src${item}`]
          .filter((fileName) => fileName.endsWith("css"))
          .map((fileName) => `<link rel="stylesheet" crossorigin="" href="${fileName}">`)
          .join("");
      }
    });
  }

  // passing SSR context object which will be available via useSSRContext()
  // @vitejs/plugin-vue injects code into a component's setup() that registers
  // itself on ctx.modules. After the render, ctx.modules would contain all the
  // components that have been instantiated during this render call.
  const ctx = {};
  const renderProcess = [renderToString(app, ctx)];
  if (!param.urlref) {
    renderProcess.push(getAdActivityListV2());
  }
  const [html, adSpace] = await Promise.all(renderProcess);
  if (!param.urlref) {
    store.$state.adSpace = adSpace;
  }
  const status = store.errorCode;
  const title = store.INITIAL_TITLE || "借贷";
  const ret = {
    status,
    store,
    html,
    data: `window.__INITIAL_STATE__ = ${JSON.stringify(store.$state)}`,
    head,
    title,
  };
  app.unmount();
  store.$state = null;
  return ret;
}

export async function forwardReq() {
  const { req } = global.asyncLocalStorage.getStore();
  const url = req.originalUrl.substring(20);
  if (
    url.startsWith("/config/api") ||
    url.startsWith("/loan/api") ||
    url.startsWith("/general/api")
  ) {
    const options = { headers: {} };
    if (req.headers) {
      if (req.headers["x-mock"] === "1") {
        options.mock = true;
      }
      if (req.headers["x-wallet-key"]) {
        options.headers["x-wallet-key"] = req.headers["x-wallet-key"];
      }
    }
    if (url.startsWith("/loan/api/bff")) {
      const method = bffIntls[url.substring(14)];
      if (method) {
        const data = await method(req.fields, options);
        return data;
      }
      return {
        code: 404,
        message: "Request failed with status code 404",
      };
    }
    const data = await request(url, req.fields, options);
    return data;
  }
  return {
    code: 404,
    message: "Not Found",
  };
}

export async function queryWhiteList() {
  const res = await request("/loan/api/config/domainWhitelist", {});
  return res?.data || [];
}

export async function redirectReq() {
  const { req, res } = global.asyncLocalStorage.getStore();
  const method = redirectIntls[req.baseUrl.substring(26)];
  if (method) {
    const url = await method(req.query);
    if (typeof url === "string") {
      // TODO 这里根据path 路由不同模块，类似bff 获取url
      if (method.name === "realnameActivity") {
        res.redirect(301, url);
      } else {
        res.redirect(302, url);
      }
    } else {
      throw new Error(`Internal Error,${method} return ${url}`);
    }
  } else {
    throw new Error(`Invalid redirect:${method}`);
  }
}

export { logInterface, logRun, buildContextStore };
