import { createSSRApp } from "vue";
import NoSSR from "vue-no-ssr";
import { createPinia } from "pinia";
import Vue3TouchEvents from "vue3-touch-events";

import "@hihonor/hnr/dist/style.css";
import hnr from "@hihonor/hnr/dist/hnr.es.min";

import getPageComponentByUrl from "./helpers/router-helper";
import myBank from "./components/myBank/index.vue";

export default {
  components: {
    "no-ssr": NoSSR,
  },
};
// SSR requires a fresh app instance per request, therefore we export a function
// that creates a fresh app instance. If using Pinia, we'd also be creating a
// fresh store here.
export async function createApp(url, param = {}) {
  const [App, useStore, file] = await getPageComponentByUrl(url);
  const pinia = createPinia();
  const app = createSSRApp(App);
  app.use(pinia);
  app.use(hnr);
  app.use(Vue3TouchEvents, { swipeTolerance: 100 });

  // 银行卡组件注册
  app.component("MyBank", myBank);
  const store = useStore(pinia);
  store.$state.param = param;

  // 服务端 store是模块导出有单例陷阱，所以要用解构实现每次送入不同的初始化实例
  return { app, store, file };
}

export const DEFAULT_TIMEOUT = 60000;
