import axios from "axios";
import { generateRandom, isProductionEnv, sha256Hex } from "./utils";
import { logGeneral, logRun } from "./logger/log-util";
import { DEFAULT_TIMEOUT } from "../main";
import { isNumber } from "./mathUtils";

const domainMap = {
  // development: "http://card-dev-drcn.inner.wallet.hihonorcloud.com",
  development: "http://localhost:18007",
  // development: "http://localhost:3000/mock/26",
  fat: "http://card-dev-drcn.inner.wallet.hihonorcloud.com",
  // development: "http://card-test-drcn.inner.wallet.hihonorcloud.com",
  sit: "http://card-sit-drcn.wallet.hihonorcloud.com",
  pre: "http://wallet-card-uat-drcn.hihonorcloud.com",
  production: "http://card-drcn.inner.wallet.hihonorcloud.com",
};

const axiosInstance = axios.create({
  baseURL: domainMap[process.env.DEF_ENV_TYPE],
  timeout: DEFAULT_TIMEOUT,
});

function buildHeader(req, context, config) {
  const { url, data = {} } = config;
  const headers = {};
  const timeStamp = new Date().getTime();
  headers.nonce = `${timeStamp}${generateRandom(0, 3)}`;
  headers.timeStamp = timeStamp;
  headers["x-uuid"] =
    `${generateRandom(1, 8)}-${generateRandom(1, 4)}-${generateRandom(1, 4)}-${generateRandom(
      1,
      4,
    )}-${generateRandom(1, 12)}`;
  headers["access-token"] = context.userInfo?.accessToken;
  headers["x-wallet-env"] = process.env.WALLET_ENV || "";
  headers["x-oaid"] = context["x-oaid"];
  headers.mockFlag = context.mockFlag || "";
  const { country, language } = context.deviceInfo;
  data.country = country;
  data.language = language;
  data.deviceId = context.userInfo?.deviceId || "xxx";
  data.deviceOsVersion = context.agentInfo?.os?.version;
  data.clientPlatform = (context.agentInfo?.os?.name || "").toLowerCase() === "android" ? 1 : 2;
  data.cplc = "NONNFC";
  data.deviceType = 1;
  const reqConfig = config;
  reqConfig.data = data;
  if (config.headers && config.headers["x-wallet-key"]) {
    headers["x-wallet-key"] = config.headers["x-wallet-key"];
  }
  const sign = `${timeStamp}|${headers.nonce}|${url}|${JSON.stringify(data)}`;
  headers["x-sign"] = sha256Hex(sign);
  if (context.deviceInfo.packageName === "com.hihonor.id") {
    headers.hnidVersion = context.deviceInfo.versionCode;
  }
  headers.traceId = context.logContext.traceId;
  const loc = encodeURIComponent(context.userInfo["x-loc"] || "");

  // 创建一个headers的副本，以便修改DxmCreditInfoResponse
  const sanitizedHeaders = { ...headers };
  const sanitizedData = { ...data };
  // 删除敏感信息
  delete sanitizedHeaders["x-wallet-key"];
  delete sanitizedData.encryptedParams;
  logRun(
    `requset url is: ${url}, header is: ${JSON.stringify(sanitizedHeaders)}, data is: ${JSON.stringify(sanitizedData)}`,
  );

  return { ...headers, ...context.deviceInfo, ...context.userInfo, "x-loc": loc };
}

function logServerRequest(response, type) {
  const { context } = global.asyncLocalStorage.getStore();
  const { logContext } = context;
  const req = response.request;
  const message = {
    deviceType: 1,
    result: 0,
    traceId: logContext.traceId,
    p: "Wallet",
    s: "wallet-loan-web",
    t: new Date().getTime(),
    deviceModel: logContext.deviceModel,
    id: "100000091",
    methodName: req?.path,
    userId: logContext.userId,
    deviceId: logContext.deviceId,
  };
  if (req && req.getHeader) {
    message.costTime = new Date().getTime() - parseInt(req.getHeader("timeStamp"), 10);
  }
  if (type === "error") {
    message.result = 1;
    message.failReason = response.message;
    logRun(`${req.path}-${response.message}`, "error");
  }
  logGeneral(message);
}

axiosInstance.interceptors.request.use(
  (config) => {
    const { req, context } = global.asyncLocalStorage.getStore();
    const reqConfig = config;
    if (config.url === "/loan/api/user/withholdSignUrl") {
      const { userInfo } = context;
      const url = `https://${req.headers.host}/wallet-loan-web/pages/transfer?canBack=true`;
      reqConfig.data.returnUrl = `wallet://business/loan?cid=${userInfo.cid}&subCid=${userInfo["x-sub-cid"]}&loadUrl=${encodeURIComponent(url)}`;
    }

    reqConfig.headers = buildHeader(req, context, config);

    // 根据mockFlag动态替换baseURL为mock地址
    if (!isProductionEnv() && reqConfig.headers.mockFlag && isNumber(reqConfig.headers.mockFlag)) {
      reqConfig.baseURL = `https://card-dev-drcn.wallet.hihonorcloud.com/mock/${reqConfig.headers.mockFlag}`;
    }

    req.headers.traceId = context.logContext.traceId;
    return config;
  },
  (error) =>
    // Do something with request error
    Promise.reject(error),
);

axiosInstance.interceptors.response.use(
  (response) => {
    // Any status code that lie within the range of 2xx cause this function to trigger
    // Do something with response data
    logServerRequest(response, "info");
    const { req, res } = global.asyncLocalStorage.getStore();
    if (response.data?.code === 1201 && !req.cookies.access_fail) {
      if (req.originalUrl.startsWith("/wallet-loan-web/pages")) {
        res.cookie("access_fail", "1");
      }
    }
    return response;
  },
  (error) => {
    // Any status codes that falls outside the range of 2xx cause this function to trigger
    // Do something with response error
    logServerRequest(error, "error");
    return Promise.reject(error);
  },
);

export default axiosInstance;
