export const DEBOUNCE_OPTIONS = {
  leading: true,
  trailing: false,
  maxWait: 3000,
};
export const DEBOUNCE_WAIT_MILLISECONDS = 1000;

const AGREE_PROD_DOMAIN_PARAM = "https://agreement.itsec.honor.com";
const AGREE_TEST_DOMAIN_PARAM = "https://agreement-sit.test.honor.com";
const INTRO_PROD_DOMAIN_PARAM = "https://contentplatform-drcn.hihonorcdn.com/honorWallet";
const INTRO_TEST_DOMAIN_PARAM = "https://content-test-drcn.hihonorcdn.com/honorWallet/test";
const INTRO_DEVELOPMENT_DOMAIN_PARAM = "https://content-test-drcn.hihonorcdn.com/honorWallet/dev";

export const SET_NETWORK_EVENT = "SET_NETWORK_EVENT";
export const USER_PROTOCAL_CODE = 1493;
export const PRIVACY_PROTOCAL_CODE = 1492;

export const STORE_VERSION_CODE = "905";
export const MATERIAL_VERSION_CODE = "906";
const VERSION_CODE_MAP_STRING = /\$\{versionCode\}/;

export const LOAN_APPLY_ERROR_CODE_MAP = {
  LOAN_EXPIRED: 171013,
};
export const LOAN_VERIFY_ERROR_CODE_MAP = {
  TRIAL_EXPIRED: 171013,
};

const PROTOCAL_URL = (domainParam, agrNo, theme = "light") => {
  let url = `${domainParam}/asm/agrFile/getHtmlFile?agrNo=${agrNo}&country=cn&branchId=0&langCode=zh-cn`;
  if (theme === "dark") {
    url += "&themeName=dark";
  }
  return url;
};

const INRTODUCTION_URL = (domainParam) => {
  return `${domainParam}/loan-productIntroduce/${MATERIAL_VERSION_CODE}/index.html`;
};

const COUPON_USE_RULE_URL = (domainParam) => {
  return `${domainParam}/loan-couponUseRule/${MATERIAL_VERSION_CODE}/index.html`;
};

export const domainMap = {
  "card-dev-drcn.wallet.hihonorcloud.com": "development",
  "wallet-web-dev-drcn.cloud.honor.com": "development",
  "card-test-drcn.wallet.hihonorcloud.com": "test",
  "wallet-web-test-drcn.cloud.honor.com": "test",
  "card-sit-drcn.wallet.hihonorcloud.com": "test",
  "wallet-card-uat-drcn.hihonorcloud.com": "production",
  "wallet-api-uat-drcn.cloud.honor.com": "production",
  "card-drcn.wallet.hihonorcloud.com": "production",
  "wallet-web-uat-drcn.hihonorcloud.com": "production",
  "wallet-web-drcn.cloud.honor.com": "production",
};

/**
 * 获取用户协议的URL
 * @param {string} theme
 * @returns
 */
export const getUserProtocalUrl = (theme = "light") => {
  const domain = window.location.host;
  if (domainMap[domain] === "production") {
    return PROTOCAL_URL(AGREE_PROD_DOMAIN_PARAM, USER_PROTOCAL_CODE, theme);
  }
  return PROTOCAL_URL(AGREE_TEST_DOMAIN_PARAM, USER_PROTOCAL_CODE, theme);
};

/**
 * 获取隐私协议的URL
 * @param {string} theme
 * @returns
 */
export const getPrivacyProtocalUrl = (theme = "light") => {
  const domain = window.location.host;
  if (domainMap[domain] === "production") {
    return PROTOCAL_URL(AGREE_PROD_DOMAIN_PARAM, PRIVACY_PROTOCAL_CODE, theme);
  }
  return PROTOCAL_URL(AGREE_TEST_DOMAIN_PARAM, PRIVACY_PROTOCAL_CODE, theme);
};

/**
 * 获取介绍物料页的URL
 * @returns
 */
export const getIntroductionPageUrl = () => {
  const domain = window.location.host;
  if (domainMap[domain] === "production") {
    return INRTODUCTION_URL(INTRO_PROD_DOMAIN_PARAM);
  }
  if (domainMap[domain] === "test") {
    return INRTODUCTION_URL(INTRO_TEST_DOMAIN_PARAM);
  }
  if (domainMap[domain] === "development") {
    return INRTODUCTION_URL(INTRO_DEVELOPMENT_DOMAIN_PARAM);
  }
  return INRTODUCTION_URL(INTRO_PROD_DOMAIN_PARAM);
};

/**
 * 获取优惠券使用规则的URL
 * @returns
 */
export const getCouponUseRuleUrl = () => {
  const domain = window.location.host;
  if (domainMap[domain] === "production") {
    return COUPON_USE_RULE_URL(INTRO_PROD_DOMAIN_PARAM);
  }
  if (domainMap[domain] === "test") {
    return COUPON_USE_RULE_URL(INTRO_TEST_DOMAIN_PARAM);
  }
  if (domainMap[domain] === "development") {
    return COUPON_USE_RULE_URL(INTRO_DEVELOPMENT_DOMAIN_PARAM);
  }
  return COUPON_USE_RULE_URL(INTRO_PROD_DOMAIN_PARAM);
};

export const versionCodeMapUrl = (url) => {
  // 如果包含版本号映射符，则替换为实际版本号
  if (VERSION_CODE_MAP_STRING.test(url)) {
    return url.replace(VERSION_CODE_MAP_STRING, MATERIAL_VERSION_CODE);
  }
  // 如果不包含版本号映射符，则手动处理实际版本号
  const uRL = new URL(url);
  const pathSegs = uRL.pathname.split("/");
  pathSegs.splice(-1, 0, MATERIAL_VERSION_CODE);
  uRL.pathname = pathSegs.join("/");
  return uRL.toString();
};

export const urlMap = {
  loan_index: "/wallet-loan-web/pages/index",
  credit_realname: "/wallet-loan-web/pages/credit/realname",
  credit_realname_activity: "/wallet-loan-web/pages/credit/realname",
  credit_sign: "/wallet-loan-web/pages/credit/sign",
  loan_calc: "/wallet-loan-web/pages/loan/calc",
  loan_sign: "/wallet-loan-web/pages/loan/sign",
  loan_infoform: "/wallet-loan-web/pages/loan/infoform",
  loan_sms: "/wallet-loan-web/pages/loan/sms",
  sdk_credit_face: "/wallet-loan-web/pages/credit/detection",
  sdk_loan_face_confirm: "/wallet-loan-web/pages/loan/detection",
};

let currentStore = null;
export function getCurrentStore() {
  // eslint-disable-next-line no-underscore-dangle
  return window.__INITIAL_STATE__ || currentStore;
}

export function setCurrentStore(store) {
  currentStore = store;
}

let axiosInstance = null;
export function setAxiosInstance(instance) {
  axiosInstance = instance;
}
export function getAxiosInstance() {
  return axiosInstance;
}

export const supplierIdMap = {
  dxm: 1,
  jd: 5,
  lx: 6,
  qf: 7,
};
