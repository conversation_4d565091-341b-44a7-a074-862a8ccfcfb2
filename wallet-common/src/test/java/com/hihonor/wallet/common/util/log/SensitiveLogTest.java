package com.hihonor.wallet.common.util.log;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static org.junit.Assert.*;

/**
 * 功能描述
 *
 * @since 2022-06-08
 */
@Slf4j
public class SensitiveLogTest {
    private final static String OMMITED=  "ommited...";


    @Test
    public void hideMarkIpStr_FunctionWithMarkIpStrAndGetHideStr_ReturnsCorrectHiddenString() {
        String needHideMark = "1234567890";
        String result = SensitiveLog.hideMarkIpStr(needHideMark);
        String expectedOutcome = "*-*-*-*";
        assertEquals(expectedOutcome, result);
    }
    @Test
    public void testGetHideStr_Function() {
        // Input
        String needHideMark = "1234567890";
        int startSize = 3;
        int endSize = 3;
        int length = 10;
        String expectedOutcome = "*-*-*-*-*-*-*-*-*-*";
        String actualOutcome = SensitiveLog.getHideStr(needHideMark, startSize, endSize, length);
        assertEquals(expectedOutcome, actualOutcome);
    }
    @Test
    public void test_markIpStr_Function() {
        String needHideMark = "1234567890";
        String expectedOutcome = "1234567890"; // Assuming the expected outcome is the same as the input
        String actualOutcome = SensitiveLog.markIpStr(needHideMark);
        assertEquals(expectedOutcome, actualOutcome);
    }


    @Test
    public void testHideMarkIpStr_PerformanceTest_ManyShortInputs() {
        String[] needHideMark = new String[10000];
        for (int i = 0; i < 10000; i++) {
            needHideMark[i] = String.format("%04d", i);
        }
        long startTime = System.currentTimeMillis();
        String[] actualOutcome = new String[needHideMark.length];
        for (int i = 0; i < needHideMark.length; i++) {
            actualOutcome[i] = SensitiveLog.hideMarkIpStr(needHideMark[i]);
        }
        long endTime = System.currentTimeMillis();
        assert endTime - startTime < 1000;
    }
    @Test
    public void hideMarkIpStr_EdgeCaseInput_Length5() {
        String needHideMark = "12345";
        String expectedOutcome = "*-*-*"; // This is the expected outcome based on the function's logic
        String actualOutcome = SensitiveLog.hideMarkIpStr(needHideMark);
        assertEquals(expectedOutcome, actualOutcome);
    }
    @Test
    public void hideMarkIpStr_EdgeCaseInput_Length4() {
        String needHideMark = "1234";
        String expectedOutcome = "*234"; // or the expected outcome based on the function's logic
        String actualOutcome = SensitiveLog.hideMarkIpStr(needHideMark);
        assertEquals(expectedOutcome, actualOutcome);
    }
    @Test
    public void hideMarkIpStr_InputWithOnlyZeros_ReturnsCorrectHiddenString() {
        String needHideMark = "0000";
        String expectedOutcome = "*000"; // or the expected outcome based on the function's logic
        String actualOutcome = SensitiveLog.hideMarkIpStr(needHideMark);
        assertEquals(expectedOutcome, actualOutcome);
    }
    @Test
    public void hideMarkIpStr_InputWithTrailingZeros_ReturnsCorrectHiddenString() {
        String needHideMark = "1234000";
        String expectedOutcome = "*-*-*-*0000";
        String actualOutcome = SensitiveLog.hideMarkIpStr(needHideMark);
        assertEquals(expectedOutcome, actualOutcome);
    }
    @Test
    public void hideMarkIpStr_InputWithLeadingZeros_ReturnsCorrectHiddenString() {
        String needHideMark = "001234";
        String expectedOutcome = "*1234"; // or the expected outcome based on the function's logic
        String actualOutcome = SensitiveLog.hideMarkIpStr(needHideMark);
        assertEquals(expectedOutcome, actualOutcome);
    }
    @Test
    public void hideMarkIpStr_InputWithNonNumericCharacters_ReturnsCorrectHiddenString() {
        String needHideMark = "123abc456";
        String expectedOutcome = "*-*-*-*"; // This is the expected outcome based on the function's logic
        String actualOutcome = SensitiveLog.hideMarkIpStr(needHideMark);
        assertEquals(expectedOutcome, actualOutcome);
    }
    @Test
    public void hideMarkIpStr_ShortInput() {
        String needHideMark = "1234";
        String expectedOutcome = "*234"; // or the correct expected outcome based on the function's logic
        String actualOutcome = SensitiveLog.hideMarkIpStr(needHideMark);
        assertEquals(expectedOutcome, actualOutcome);
    }

    @Test
    public void hideMarkIpStr_NullInput_ReturnsEmptyString() {
        String needHideMark = null;
        String expected = "";
        String result = SensitiveLog.hideMarkIpStr(needHideMark);
        assertEquals(expected, result);
    }
    @Test
    public void hideMarkIpStr_EmptyInput_ReturnsEmptyString() {
        String needHideMark = "";
        String result = SensitiveLog.hideMarkIpStr(needHideMark);
        assertEquals("", result);
    }

    @Test
    public void hideMarkLog_LogStringWithOutOfMemoryError_ReturnsOriginalString() {
        String logStr = "This is a log string that throws an out of memory error";
        String result = SensitiveLog.hideMarkLog(logStr);
        assertEquals(logStr, result);
    }
    @Test
    public void hideMarkLog_NullPointerException_ReturnsOriginalString() {
        String logStr = null;
        String result = SensitiveLog.hideMarkLog(logStr);
        assertEquals(null, result);
    }
    @Test
    public void hideMarkLog_LogStringWithException_ReturnsOriginalString() {
        String logStr = "This is a log string that throws an exception";
        String expectedOutcome = logStr;
        String actualOutcome = SensitiveLog.hideMarkLog(logStr);
        assertEquals(expectedOutcome, actualOutcome);
    }


    @Test
    public void hideMarkLog_LogStringWithLettersOnly_ReturnsEncryptedString() {
        String logStr = "abcdefghijklmnopqrstuvwxyz";
        String result = SensitiveLog.hideMarkLog(logStr);
        assertEquals("Expected the function to return the encrypted log string", result, SensitiveLog.hideMarkLog(logStr));
    }

    @Test
    public void hideMarkLog_LogStringWithNumbersOnly_ReturnsEncryptedString() {
        String logStr = "1234567890123456";
        String result = SensitiveLog.hideMarkLog(logStr);
        assertEquals("ommited...", result);
    }


    @Test
    public void hideMarkLog_LogStringWithChineseCharacters_ReturnsEncryptedString() {
        String logStr = "This is a log string with Chinese characters: ";
        String result = SensitiveLog.hideMarkLog(logStr);
        assertNotNull(result);
    }

    @Test
    public void hideMarkLog_LogStringWithSpecialCharacters_ReturnsEncryptedString() {
        String logStr = "This is a log string with special characters: !@#$%^&*()";
        String result = SensitiveLog.hideMarkLog(logStr);
        assertNotNull(result);
    }
    @Test
    public void hideMarkLog_LogStringLongerThan1000Characters_ReturnsOmittedString() {
        StringBuilder logStr = new StringBuilder("This is a very long log string that is longer than 1000 characters...");
        for (int i = 0; i < 1000; i++) {
            logStr.append("a");
        }
        String expectedOutcome = "ommited...";
        String actualOutcome = SensitiveLog.hideMarkLog(logStr.toString());
        assertEquals(expectedOutcome, actualOutcome);
    }
    

    @Test
    public void hideMarkLog_LogStringWithSensitiveInformationButNoConfiguration_ReturnsOriginalString() {
        String logStr = "This is a log string with sensitive information: 1234567890123456";
        ConcurrentHashMap<String, String> KEY_REG_MAP = new ConcurrentHashMap<>();
        KEY_REG_MAP.clear(); // simulate no configuration
        String result = SensitiveLog.hideMarkLog(logStr);
        assertEquals(logStr, result);
    }

    @Test
    public void hideMarkLog_LogStringWithMultipleSensitiveInformation_ReturnsEncryptedString() {
        String logStr = "This is a log string with multiple sensitive information: 1234567890123456, John Doe, 13812345678";
        String result = SensitiveLog.hideMarkLog(logStr);
        assertNotEquals(logStr, result);
    }
    @Test
    public void hideMarkLog_LogStringWithSensitiveInformation_ReturnsEncryptedString() {
        String logStr = "This is a log string with sensitive information: 1234567890123456";
        String result = SensitiveLog.hideMarkLog(logStr);
        assertNotEquals(logStr, result);
    }
    @Test
    public void hideMarkLog_NullLogStringInput_ReturnsNull() {
        String logStr = null;
        String result = SensitiveLog.hideMarkLog(logStr);
        assertEquals(null, result);
    }
    @Test
    public void hideMarkLog_EmptyLogString_ReturnsOriginalString() {
        String logStr = "";
        String result = SensitiveLog.hideMarkLog(logStr);
        assertEquals("", result);
    }

    @Test
    public void hideMarkStr_EmptyStringInput_ReturnsEmptyString() {
        String needHideMark = "";
        String result = SensitiveLog.hideMarkStr(needHideMark);
        assertEquals("", result);
    }


    @Test
    public void hideMarkStr_NullInput_ReturnsEmptyString() {
        String needHideMark = null;
        String expected = "";
        String result = SensitiveLog.hideMarkStr(needHideMark);
        assertEquals(expected, result);
    }


    @Test
    public void hideMarkStr_SingleCharacterInput() {
        String needHideMark = "a";
        String expected = "*";
        String result = SensitiveLog.hideMarkStr(needHideMark);
        assertEquals(expected, result);
    }


    @Test
    public void hideMarkStr_ShortStringInput() {
        String needHideMark = "abcd";
        String expected = "*bcd";
        String result = SensitiveLog.hideMarkStr(needHideMark);
        assertEquals(expected, result);
    }

    @Test
    public void hideMarkStr_MediumStringInput() {
        String needHideMark = "abcdefgh";
        String expectedOutcome = "*-*-****";
        String actualOutcome = SensitiveLog.hideMarkStr(needHideMark);
        
        assertEquals(expectedOutcome, actualOutcome);
    }

    @Test
    public void hideMarkStr_LongStringInput_ReturnsOMMITTED() {
        StringBuilder needHideMark = new StringBuilder("a");
        for (int i = 0; i < 10001; i++) {
            needHideMark.append("a");
        }
        String expectedOutcome = OMMITED;
        String actualOutcome = SensitiveLog.hideMarkStr(needHideMark.toString());
        assertEquals(expectedOutcome, actualOutcome);
    }

    @Test
    public void hideMarkStr_EdgeCaseInput_Length4() {
        String needHideMark = "abcd";
        String expectedOutcome = "*bcd";
        String actualOutcome = SensitiveLog.hideMarkStr(needHideMark);
        assertEquals(expectedOutcome, actualOutcome);
    }


    @Test
    public void hideMarkStr_EdgeCaseInput_Length1() {
        String needHideMark = "a";
        String expected = "*";
        String actual = SensitiveLog.hideMarkStr(needHideMark);
        assertEquals(expected, actual);
    }


    @Test
    public void hideMarkStr_NonASCIICharacters() {
        String needHideMark = "abcdé";
        String expectedOutcome = "a***é";
        String result = SensitiveLog.hideMarkStr(needHideMark);
        assertEquals(expectedOutcome, result);
    }


    @Test
    public void hideMarkStr_WithWhitespaceCharacters() {
        String needHideMark = "a b c d";
        String expectedOutcome = "* * * *";
        String result = SensitiveLog.hideMarkStr(needHideMark);
        assertEquals(expectedOutcome, result);
    }

    @Test
    public void hideMarkStr_WithNumbers() {
        String needHideMark = "a1b2c3d";
        String expected = "a***c***d";
        String result = SensitiveLog.hideMarkStr(needHideMark);
        assertEquals(expected, result);
    }



}

