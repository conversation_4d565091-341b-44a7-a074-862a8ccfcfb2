/*
 * Copyright (c) Honor Terminal Co., Ltd. 2022-2022. All rights reserved.
 *
 */

package com.hihonor.wallet.common.exception;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 通用错误码（4位），业务错误码（6位）
 *
 * <AUTHOR>
 * @since 2022-06-02
 */
@AllArgsConstructor
@Getter
public enum WalletResultCode implements IResultCode {
    // ----------------------------公共类------------------------------
    /**
     * 正常状态码
     */
    SUCCESS(0, "success"),

    /**
     * 服务器内部异常
     */
    @Deprecated
    INTERNAL_SERVER_ERROR(500, "service internal error! error message: {0}"),

    // 校验请求
    /**
     * 缺少必要的参数
     */
    REQUEST_FIELD_MISSING(1001, "required field(s) missing: {0}"),
    /**
     * 非法参数异常
     */
    ILLEGAL_PARAM(1002, "Illegal parameter exception :{0}"),

    /**
     * 缺少必要 的请求头
     */
    HEADER_MISSING(1003, "request header missing: {0}"),
    /**
     * 不支持的请求
     */
    METHOD_NOT_ALLOWED(1004, "method not allowed: {0}"),
    /**
     * 校验参数异常
     */
    VALIDATED_PARAM(1005, "Validate parameter exception :{0}"),

    /**
     * feign接口调用超时
     */
    FEIGN_TIME_OUT(1006, "feign接口调用超时"),

    /**
     * 三方接口网络异常
     */
    THIRD_NETWORK_ERROR(1007, "三方接口{}网络异常"),

    /**
     * 加密异常
     */
    ENCRYPT_ERROR(1101, "encrypt error: {0}"),
    /**
     * 解密异常
     */
    DECRYPT_ERROR(1102, "decrypt data err: {0}"),

    /**
     * huks
     */
    SIGNATURE_INVALID(1301, "huks auth signature invalid"),
    SIGNATURE_VERIFY_ERROR(1302, "huks signature verify error"),
    ENCRYPTION_KEY_NOT_FOUND(1303, "encryption key not found or expired"),
    HUKS_PUBLIC_KEY_NOT_FOUND(1304, "huks publicKey is not found"),
    HUKS_CERTIFICATION_EXPIRED(1305, "huks certification is expired"),
    HUKS_EMPTY_CERTIFICATE_CHAIN(110101, "huks certification chain is empty"),
    HUKS_NO_ROOT_CERT(110102, "huks root cert or cert factory init fail"),
    HUKS_AUTH_CERTIFICATION_FAIL(110105, "fail to resolve huks certification"),
    HUKS_AUTH_CERTIFICATION_INVALID(110106, "auth huks certification invalid"),
    HUKS_CHALLENGE_NOT_FOUND(110109, "huks challenge not found or expired"),
    HUKS_CERT_CHALLENGE_NOT_FOUND(110110, "huks challenge not found in the cert"),
    HUKS_CHALLENGE_NOT_MATCH(110111, "huks challenge not match"),

    /**
     * 校验用户级at失败
     */
    VERIFY_OAUTH_TOKEN_ERROR(1201, "Failed to verify user-level AT"),

    /**
     * 请求锁超时
     */
    LOCK_FAIL(1401, "Get redis lock fail"),

    /**
     * 防重放时间戳timestamp过期
     */
    REPLAY_TIMESTAMP_EXPIRED(3102, "timestamp is expired"),

    /**
     * 放重放重复请求
     */
    REPLAY_REQUEST_REPEAT(3103, "Repeat Request!"),

    /**
     * nonce格式错误
     */
    REPLAY_NONCE_ERROR(3104, "Nonce Error!"),

    /**
     * 并发请求 --针对雪球
     */
    CONCURRENT_REQUEST(3105, "Concurrent Requests"),

    /**
     * 包名及其指纹验证失败
     */
    PACKAGE_INVALID(3202, "pacakgen-name or package-finger invalid"),

    /**
     * 参数签名校验不通过
     */
    SIGN_INVALID(3203, "Parameter signature verification failed"),

    /**
     * functionCallId无效
     */
    FUNCTION_ID_INVALID(3301, "Invalid functionCallId"),

    /**
     * 4001 停服报错
     */
    THE_SERVICE_IS_STOP(4001, "服务停止"),

    // ----------------------auth请求-----------------------------------
    /**
     * authCode请求参数报错
     */
    AUTH_PARAMS_CANNOT_BE_NULL(200001, "AuthCode and SessionId cannot be null at the same time"),

    /**
     * 获取用户详细信息失败
     */
    AUTH_CODE_CANNOT_BE_NULL(200002, "AuthCode cannot be null "),


    // ----------------------push接入--------------------------------------
    GET_PUSH_ACCESS_TOKEN_ERROR(200005, "Failed to get push application-level AT :{0}"),

    PUSH_MESSAGE_ERROR(200006, "Failed to push message :{0}"),

    /**
     * AMS服务异常
     */
    AMS_SERVICE_ERROR(200007, "Ams interface: {0} response is empty"),

    /**
     * 非法国家码
     */
    NOT_SUPPORTED_COUNTRY(200008, "Protocols with country code {0} are not supported"),

    /**
     * 风控拒绝下发短信
     */
    RISK_CONTROL_REFUSE_SEND_SMSCODE(200009, "风控拒绝下发短信"),

    /**
     * 商推服务异常
     */
    HONOR_ADS_SERVICE_ERROR(2000010, "商推服务异常"),

    /**
     * WO-token认证失败
     */
    WISOPER_TOKEN_AUTH_FAIL(250001, "Failed to authenticate WiseOper-Token."),
    /**
     * 操作人权限认证失败
     */
    WISOPER_AUTH_FAIL(250002, "Failure to authenticate the operator."),
    /**
     * 会话过期
     */
    WISOPER_TOKEN_EXPIRED(250003, "Token expired!"),
    /**
     * WO接口:{0} 服务异常
     */
    WISEOPER_SERVICE_ERROR(250004, "WiseOper interface service exception."),
    /**
     * WO-Token获取失败
     */
    WISEOPER_GET_TOKEN_FAIL(250005, "Failed to access WiseOper-Token."),
    /**
     * 禁止跨站访问
     */
    WISEOPER_NO_CSRF(250006, "Cross domain access is not allowed :{0}"),

    WISEOPER_GET_ACCOUNT_FAIL(250007, "Failed to get WiseOper-Account"),
    /**
     * 无荣耀账号信息
     */
    NO_EXIST_SITE_INFO(250007, "no site information"),
    /**
     * H Core版本号重复，请重新输入
     */
    H_VERSION_REPEAT_ERROR(250008, "H Core版本号重复，请重新输入"),

    /**
     * tsm failed
     */
    TSM_FAILED(110201, "request tsm failed! TSM的报错返回值：status: {0}  desc: {1}"),

    ACCESS_CARD_NUM_SUPER(110401, "The number of access control cards has reached the upper limit"),

    CARD_PRODUCT_INVALID(110301, "卡片产品不存在"),
    CARD_ORDER_INVALID(110302, "卡片不存订单不存在"),

    APP_SIGN_INVALID(110303, "APP签名错误"),

    SP_SIGNATURE_INVALID(110200, "sp signature invalid"),

    /**
     * UID不存在
     */
    ACCESS_CARD_ID_NOT_EXIST(120401, "用户不存在该门禁卡"),

    /**
     * TSM调用失败
     */
    TSM_STATUS_ERROR_EXCEPTION(120402, "TSM调用失败;TSM的报错返回值：status: {0}  desc: {1}"),


    /**
     * 更新门禁卡失败
     */
    UPDATE_FAIL(120403, "更新门禁卡失败"),

    /**
     * 更新门禁卡失败
     */
    UPDATE_FAIL_FOR_NAME(120406, "更新门禁卡失败,门禁卡名称重复"),

    /**
     * 门禁卡名称长度不符合
     */
    LENGHT_CHECK_FAIL(120405, "字符串长度不符合"),

    /**
     * 该用户不存在门禁卡
     */
    USER_NOT_EXIST_ACCESS_CARD(120201, "该用户不存在门禁卡"),
    /**
     * 该设备该门禁卡已存在
     */
    USER_ALREADY_EXIST_ACCESS_CARD(120303, "该设备该门禁卡已存在"),
    /**
     * 卡片删除中，请先完成删除
     */
    USER_ACCESS_CARD_DELETEING(120304, "卡片删除中，请先完成删除"),
    /**
     * 其他 帐 号在当前设备已存在此门禁卡
     */
    OTHER_USER_ALREADY_EXIST_ACCESS_CARD(120306, "其他帐号在当前设备已存在此门禁卡"),

    /**
     * 当前设备存在其他账号的门禁卡
     */
    EXIST_NOT_CUR_USER_ACCESS_CARD(120307, "当前设备存在其他帐号的门禁卡"),

    /**
     * TSM调用失败
     */
    TSM_STATUS_ERROR(120404, "TSM调用失败"),

    /**
     * 当前设备已存在此门禁卡
     */
    CUR_DEVICE_EXIST_ACCESSCARD(120902, "当前设备已存在此门禁卡"),

    /**
     * SP-TSM接口超时
     */
    SP_TSM_TIME_OUT(130000, "调用通卡公司接口超时:{0}"),

    /**
     * SP-TSM接口报错
     */
    SP_TSM_ERROR(130001, "调用通卡公司接口报错"),

    /**
     * CAR-TSM接口超时
     */
    CAR_TSM_TIME_OUT(130005, "调用车钥匙公司接口超时:{0}"),

    /**
     * 订单ID不存在
     */
    ORDER_NOT_EXIST(130901, "订单ID不存在"),

    /**
     * 订单未完成支付
     */
    ORDER_NOT_PAID(131001, "订单未完成支付"),

    /**
     * 如果createOrder返回的订单是应用外订单，则报错
     */
    OUT_APP_ORDER_ERROR(131101, "开卡失败，第三方APP年内存在未完成订单"),

    /**
     * 程序错误:actionTYpe参数错误
     */
    ACTIONTYPE_ERROR(131102, "程序错误:actionType参数错误"),

    /**
     * 处理失败:卡片状态错误
     */
    CARD_STATUS_ERROR(131103, "处理失败:卡片状态错误"),

    /**
     * 开卡失败:SEI-TSM返回失败
     */
    SEI_OPEN_CARD_ERROR(131104, "开卡失败:SEI-TSM返回失败"),

    /**
     * 开卡失败:SP-TSM返回失败
     */
    SP_OPEN_CARD_ERROR(131105, "开卡失败:SP-TSM返回失败"),

    /**
     * 充值失败:SP-TSM返回失败
     */
    SP_RECHARGE_CARD_ERROR(131106, "充值失败:SP-TSM返回失败"),

    /**
     * 开卡失败，联系客服处理（存疑订单，仅可人工处理场景）
     */
    SP_OPEN_CARD_MANUAL(131107, "开卡失败，联系客服处理（存疑订单，仅可人工处理场景）"),

    /**
     * 蓝牙中断
     */
    BLUETOOTH_INTERRUPTION(131208, "蓝牙中断"),

    /**
     * 充值失败，联系客服处理（存疑订单，仅可人工处理场景）
     */
    SP_RECHARGE_MANUAL(131108, "充值失败，联系客服处理（存疑订单，仅可人工处理场景）"),

    /**
     * 卡片延期失败
     */
    SP_CARD_EXTENSION_ERROR(131113, "卡片延期失败"),

    /**
     * 退卡失败：SP-TSM返回失败
     */
    SP_REFUND_CARD_ERROR(131207, "退卡失败：SP-TSM返回失败"),

    /**
     * 退卡失败：SP-TSM删卡接口返回失败
     */
    SP_DELETE_CARD_ERROR(130802, "退卡失败：SP-TSM删卡接口返回失败"),

    /**
     * 退卡失败：SEI-TSM删卡接口返回失败
     */
    SEI_DELETE_CARD_ERROR(130803, "退卡失败：SEI-TSM接口返回失败"),

    /**
     * 获取订单失败（调用通卡公司接口失败）
     */
    SP_GET_ORDER_ERROR(130805, "获取订单失败（调用通卡公司接口失败）"),

    /**
     * 上报失败（调用通卡公司接口失败）
     */
    SP_REPORT_ERROR(130806, "上报apdu失败（调用通卡公司接口失败）"),

    /**
     * 应用同步失败（调用通卡公司接口失败）
     */
    SP_APP_SYNC_ERROR(130807, "应用同步失败（调用通卡公司接口失败）"),

    /**
     * 管理应用失败（调用通卡公司接口失败）
     */
    SP_MANAGE_APP_ERROR(130808, "管理应用失败（调用通卡公司接口失败）"),

    /**
     * 有充值未完成的订单,不允许退卡
     */
    EXIST_PAID_ORDER(130809, "有充值未完成的订单,不允许退卡"),

    /**
     * 退卡提交失败
     */
    REFUND_SUBMIT_FAIL(130811, "退卡提交失败"),

    /**
     * 卡片锁定失败
     */
    LOCK_CARD_FAIL(131109, "卡片锁定失败"),

    /**
     * 卡片解锁失败
     */
    UNLOCK_CARD_FAIL(131110, "卡片解锁失败"),

    /**
     * 开卡失败，订单将会退款
     */
    OPEN_FAIL_AND_REUND(131111, "开卡失败，订单将会退款"),

    /**
     * 充值失败，订单将会退款
     */
    RECHARGE_FAIL_AND_REFUND(131112, "充值失败，订单将会退款"),

    /**
     * 通卡公司禁止跨设备迁移
     */
    CROSS_DEVICE_MOVE_IN_FAIL(131113, "通卡公司禁止跨设备迁移"),

    /**
     * 穿戴设备不支持随用随冲
     */
    WATHCH_AUTO_RECHARGE_NOT_SUPPORT(131123, "穿戴设备不支持随用随冲"),

    /**
     * 穿戴设备不支持月租卡
     */
    WATCH_MONTH_FEE_NOT_SUPPORT(131124, "穿戴设备不支持月租卡"),

    /**
     * 迁移创建订单失败
     */
    MOVE_CREATE_ORDER_FAIL(131125, "迁移创建订单失败"),

    /**
     * 迁出失败，请重试
     */
    MOVE_OUT_FAIL(131114, "迁出失败，请重试"),

    /**
     * 迁入失败，请重试
     */
    MOVE_IN_FAIL(131115, "迁入失败，请重试"),

    /**
     * 当前设备已经存在卡片数据
     */
    CARD_ALREADY_EXIST(131116, "当前设备已经存在卡片数据"),

    /**
     * 卡片已迁入，请勿重复迁入
     */
    CARD_ALREADY_MOVE_IN(131117, "卡片已迁入，请勿重复迁入"),

    /**
     * 卡片数据用户账号不一致，禁止迁移
     */
    CARD_USERID_IS_NOT_SAME(131118, "卡片数据用户账号不一致，禁止迁移"),

    /**
     * 订单已过期
     */
    THE_ORDER_IS_EXPIRE_PLEASE_WAIT(131119, "订单已过期，请重试开卡或者充值"),

    /**
     * 并发迁入,请稍候重试
     */
    MOVE_IN_REPEAT(131120, "并发迁入,请稍候重试"),

    /**
     * 并发开卡,请稍候重试
     */
    OPEN_CARD_REPEAT(131121, "并发开卡,请稍候重试"),

    /**
     * 已存在其他订单开卡成功
     */
    EXIST_OTHER_ORDER_ISSUE_SUCCESS(131122, "已存在其他订单开卡成功"),

    /**
     * sessionId程序上下文缓存不存在
     */
    SESSIONID_ERROR(131201, "sessionId程序上下文缓存不存在"),

    /**
     * 开卡失败：SEI-TSM返回失败
     */
    REPORT_RESULT_REPEAT(131202, "指令结果重复上报"),

    /**
     * 交通卡未上线
     */
    TRANS_CARD_IS_NOT_ONLINE(130601, "交通卡未上线"),

    /**
     * 交通卡测试状态，用户无权限（不是测试用户）
     */
    NOT_A_TEST_USER(130602, "交通卡测试状态，用户无权限（不是测试用户）"),

    /**
     * 设备机型不支持
     */
    THE_DEVICE_MODEL_IS_NOT_SUPPORTED(130603, "设备机型不支持"),

    /**
     * App版本不支持
     */
    APP_VERSION_DOES_NOT_SUPPORT(130604, "App版本不支持"),

    /**
     * 系统版本不支持
     */
    OS_VERSION_DOES_NOT_SUPPORT(130626, "系统版本不支持"),

    /**
     * 开卡费或充值金额不正确
     */
    OPEN_CARD_OR_RECHARGE_VALUE_IS_INCORRECT(130605, "开卡费或充值金额不正确"),

    /**
     * 活动已经结束 不在有效期
     */
    THE_PROMOTION_IS_EXPIRE(130622, "活动已经结束 不在有效期"),

    /**
     * 活动已经结束 库存已耗尽
     */
    THE_PROMOTION_IS_MAX(130623, "活动已经结束 库存已耗尽"),

    /**
     * 有效促销活动数量超过1个
     */
    THE_PROMOTION_IS_OUT_OF_ONE(130625, "有效促销活动数量超过1个"),

    /**
     * 开卡服务费发生变化，请重新下单
     */
    OPEN_CARD_VALUE_IS_INCORRECT(130624, "开卡服务费发生变化，请重新下单"),

    /**
     * 未获取到实名信息，请重新实名
     */
    NOT_REALNAME(130627, "未获取到实名信息，请重新实名"),

    /**
     * 重复开卡：已开卡，开卡中（卡片状态：applet已安装/开卡失败）
     */
    REPEATED_CARD_OPENING(130606, "重复开卡：已开卡，开卡中（卡片状态：applet已安装/开卡失败）"),

    /**
     * 卡片状态不支持充值（非正常状态）
     */
    CARD_STATUS_IS_NOT_RECHARGE(130607, "卡片状态不支持充值（非正常状态）"),

    /**
     * 有充值未完成订单
     */
    THERE_ARE_OUTSTANDING_ORDERS_FOR_RECHARGING(130608, "有充值未完成订单"),

    /**
     * 获取订单失败
     */
    FAIL_TO_GET_ORDER(130609, "获取订单失败"),

    /**
     * 当前设备存在其他账号开通的此交通卡
     */
    EXIST_ANOTHER_ACCOUNT_FOR_THIS_DEVICE(130610, "当前设备存在其他账号开通的此交通卡"),

    /**
     * 订单已退款，请重新开卡
     */
    ORDER_REFUNDED(130611, "订单已退款，请重新开卡"),

    /**
     * 卡资源不足，开卡下单失败
     */
    INSUFFICIENT_CARD_RESOURCES(130614, "卡资源不足，开卡下单失败"),

    /**
     * 超过每日充值最大限额（充值场景）
     */
    EXCEEDING_THE_MAXIMUM_DAILY_RECHARGE_LIMIT(130612, "超过每日充值最大限额"),

    /**
     * 超过每日充值最大次数（充值场景）
     */
    EXCEEDING_THE_MAXIMUM_DAILY_RECHARGE_TIME(130613, "超过每日充值最大次数"),

    /**
     * 充值金额达到上线
     */
    THE_TOTAL_RECHARGE_AMOUNT_HAS_REACHED_THE_UPPER_LIMIT(130614, "卡资源不足，开卡下单失败"),

    /**
     * 账号未实名，暂不能开卡（北京）
     */
    NEED_REALNAME(130615, "账号未实名，暂不能开卡（北京）"),

    /**
     * 该设备已参加荣耀老用户回馈礼活动
     */
    COUPON_DEVICE_RISK_ERROR(130616, "该设备已参加荣耀老用户回馈礼活动"),

    /**
     * 优惠券已使用
     */
    COUPON_USED(130617, "优惠券已使用"),

    /**
     * 优惠券已被其他订单锁定
     */
    COUPON_LOCKED(130618, "优惠券已被其他订单锁定"),

    /**
     * 优惠券不满足使用条件
     */
    COUPON_CHECK_FAILED(130619, "优惠券不满足使用条件"),

    /**
     * 优惠券不存在
     */
    COUPON_NOT_EXIST(130620, "优惠券不存在"),

    /**
     * 优惠券被风控
     */
    COUPON_RISK_ERROR(130621, "优惠券的信息存在异常"),

    /**
     * 订单已退款，且卡状态为正常
     */
    CARD_IS_NORMAL_AND_ORDER_REFUNDED(130622, "订单已退款，且卡状态为正常"),

    /**
     * 卡号错误，未查询到卡片信息
     */
    UID_ERROR_CAN_NOT_FIND_CARD_INFO(130701, "卡号错误，未查询到卡片信息"),

    /**
     * 当前卡片状态不支持退卡
     */
    THE_CARD_STATUS_IS_NOT_SUPPORT_FOR_REFUND(130801, "当前卡片状态不支持退卡"),

    /**
     * 退卡失败：SP-TSM提交退款失败
     */
    SP_TSM_REFUND_ERROR(130804, "退卡失败：SP-TSM提交退款失败"),

    /**
     * 退卡失败：SEI-TSM接口返回失败
     */
    SEI_TSM_DELETE_CARD_ERROR(130803, "退卡失败：SEI-TSM接口返回失败"),

    /**
     * 退卡重试失败：没有查询到退卡失败的订单
     */
    CAN_NOT_GET_REFUND_RECORD(130805, "退卡重试失败：没有查询到退卡失败的订单"),

    /**
     * 卡内余额异常，不允许退卡（卡内余额>maxRefundBalance）
     */
    REFUND_CARDBALANCE_MORE_THAN_MAXREFUNDBALANCE_RECORD(130812, "卡内余额异常，不允许退卡（卡内余额>maxRefundBalance）"),

    /**
     * 卡内余额异常，不允许退卡（卡内余额<minRefundBalance）
     */
    REFUND_CARDBALANCE_LESS_THAN_MINREFUNDBALANCE_RECORD(130813, "卡内余额异常，不允许退卡（卡内余额<minRefundBalance）"),

    /**
     * 开卡成功，不支持退款
     */
    OPEN_CARD_SUCCESS(133101, "开卡成功，不支持退款"),

    /**
     * 开卡成功充值失败，已申请退款
     */
    OPEN_CARD_SUCCESS_AND_RECHARGE_FAIL(133102, "开卡成功充值失败，已申请退款"),

    /**
     * 充值成功，不支持退款
     */
    RECHARGE_SUCCESS(133103, "充值成功，不支持退款"),

    /**
     * 订单存疑或不支持退款，请重试开卡/充值
     */
    ORDER_IS_DOUBT_AND_PLEASE_RETRY(133104, "订单存疑或不支持退款，请重试开卡/充值"),

    /**
     * 订单存疑，请联系客服
     */
    ORDER_IS_DOUBT_AND_PLEASE_CONTACT_CS(133105, "订单存疑，请联系客服"),

    /**
     * 订单退款失败，请联系客服
     */
    ORDER_REFUND_FAIL_PLEASE_CONTACT_CS(133106, "订单退款失败，请联系客服"),

    /**
     * 订单退款中，不用重复申请
     */
    ORDER_IS_REFUNDING_AND_PLEASE_NOT_TRY_AGAIN(133107, "订单退款中，不用重复申请"),

    /**
     * 订单退款成功，不用重复申请
     */
    ORDER_IS_REFUND_SUCCESS_AND_PLEASE_NOT_TRY_AGAIN(133108, "订单退款成功，不用重复申请"),

    /**
     * 申请退款失败，调用SP接口失败
     */
    SP_ORDER_REFUND_FAIL(133109, "申请退款失败，调用SP接口失败"),

    /**
     * 申请退款数据库操作失败
     */
    ORDER_REFUND_DB_FAIL(133110, "申请退款数据库操作失败"),

    /**
     * 可为签名校验失败
     */
    SP_VERIFY_SIGN_ERROR(130001, "SP签名验证失败"),

    /**
     * 订单已完成
     */
    ORDER_IS_COMPLETED(131812, "订单已完成"),

    /**
     * 调用通卡订单接口失败
     */
    CALL_SP_INTERFACE_FAIL(131811, "调用通卡订单接口失败"),

    /**
     * 未查询到响应的卡片信息
     */
    FIND_CARD_FAIL(132101, "未查询到响应的卡片信息"),

    /**
     * 无需重复签约
     */
    REPEAT_ENTRUST(132102, "无需重复签约"),

    /**
     * SP-TSM请求签约失败
     */
    SP_TSM_ENTRUST_FAIL(132103, "SP-TSM请求签约失败"),

    /**
     * 签约状态异常
     */
    ENTRUST_STATUS_ERROR(132104, "签约状态异常"),

    /**
     * 优惠券服务异常，领取失败
     */
    COUPON_SERVER_ERROR(132801, "优惠券服务异常，领取失败"),

    /**
     * 用户已领取过，不能重复领取
     */
    USER_RECEIVED_ERROR(132802, "用户已领取过，不能重复领取"),

    /**
     * 领取失败，批次号错误
     */
    COUPON_BATCH_ID_ERROR(132803, "领取失败，批次号错误"),

    /**
     * 领取优惠卷异常（MIA+BVL可以领取）
     */
    COUPON_STATUS_ERROR(132804, "领取失败，设备不符合领取条件"),

    /**
     * 领取优惠卷异常（MIA前注册用户可领取）
     */
    USER_STATUS_ERROR(132805, "领取失败，用户不符合领取条件"),

    /**
     * 领取失败，用户已参加此活动，不能再次参与
     */
    USER_COUPON_REPEAT_ERROR(132806, "用户已参加此活动，不能再次参与"),

    /**
     * 更新灰度比率失败原因
     */
    GRAYRATIO_RANGE_ERROR(160801, "灰度上线比率必须是0-100的整数"),

    /**
     * 更新灰度比率失败原因
     */
    GRAYRATIO_LESS_THAN_CURRENTVALUE_ERROR(160802, "必须大于"),

    /**
     * 荣耀账号错误，未查询到对应的用户
     */
    USER_ACCOUNT_ERROR(160001, "荣耀账号错误，未查询到对应的用户"),

    /**
     * 退卡失败：调用SP退款接口失败
     */
    SP_REFUND_ERROR(161001, "退卡失败：调用SP退款接口失败"),

    /**
     * 退卡失败：调用SP退卡业务接口失败
     */
    SP_CARD_REFUND_ERROR(161002, "退卡失败：调用SP退卡业务接口失败"),

    /**
     * 退卡失败：调用SP退卡业务接口失败
     */
    EXIST_UNFINISHED_RECHARGE_ORDER_REFUND_ERROR(161003, "存在充值未完成订单，不允许退卡"),

    /**
     * 非中国站点用户
     */
    NOT_CHINA_SITE_USER_ERROR(160002, "非中国站点用户"),

    /**
     * 用户白名单已存在，无需新增
     */
    USERAPPROUTE_EXISTED_ERROR(160003, "用户白名单已存在，无需新增"),

    /**
     * 卡号错误，该 帐 号下未开通此卡号的交通卡
     */
    CARD_UID_ERROR(161101, "该帐号下未开通此卡号的交通卡"),

    /**
     * 文件上传失败
     */
    FILE_UPLOADFAIL_ERROR(161202, "文件上传失败，稍后再试"),

    /**
     * 已上传射频文件，请重新输入账号
     */
    EXIST_ACCESSFILE_UPLOADFAIL_ERROR(161203, "已上传射频文件，请重新输入卡片、帐号和SN号"),

    /**
     * 图片上传失败
     */
    IMAGE_UPLOAD_ERROR(161204, "图片上传失败"),

    /**
     * 交通卡已上线状态不能修改为测试状态
     */
    CARD_STATUS_UPDATE_ERROR(167201, "已上线状态不能修改为测试状态"),

    /**
     * sei-tsm返回null
     */
    SEI_TSM_RETURN_NULL(140301, "sei-tsm返回null"),

    /**
     * seinit请求频繁，拒绝本次请求
     */
    SE_INIT_REPEAT(140302, "seinit请求频繁，拒绝本次请求"),

    /**
     * 当前设备不支持添加白名单
     */
    NO_SUPPORT_ADD_WHITE(140601, "当前设备不支持添加白名单"),

    /**
     * 还存在交通卡或者门禁卡，不能清除数据
     */
    EXIST_TRANS_OR_ACCESS(140901, "还存在交通卡或者门禁卡，不能清除数据"),

    /**
     * 活动周期开始时间不能晚于结束时间
     */
    ACTIVATION_DISCOUNT_TIME_ERROR(160007, "活动周期开始时间不能晚于结束时间"),

    /**
     * 活动周期开始时间不能晚于结束时间
     */
    ACTIVATION_DISCOUNT_FEE_ERROR(160008, "开卡服务费优惠价必须小于原开卡服务费"),

    /**
     * 上传文件格式不支持
     */
    FILE_SUFFIX_VALID_ERROR(160004, "上传文件格式不支持"),

    /**
     * 验证码错误
     */
    SNS_CODE_VERIFY_ERROR(132401, "验证码错误"),

    /**
     * cardType错误，不支持发送短信
     */
    CARD_TYPE_ERROR(132301, "cardType错误，不支持发送短信"),

    /**
     * 60s内重复发送
     */
    SNS_REPEAT_SEND(132302, "60s内重复发送"),

    /**
     * 超过1天短信发送限制次数，请明天再试
     */
    SNS_SEND_OUT_OF_LIMIT(132303, "超过1天短信发送限制次数，请明天再试"),

    /**
     * 短信发送失败
     */
    SNS_ERROR(132304, "短信下发失败"),

    /**
     * 优惠券服务异常
     */
    COUPON_SERVICE_ERROR(132701, "优惠券服务异常"),

    /**
     * 单边交易
     */
    ONE_SIDE_TRANSACTION(130821, "您的卡片存在单边交易，暂无法办理退卡"),

    /**
     * 余额异常
     */
    BALANCE_IRREGULARITY(130822, "您的卡片余额获取异常，暂无法办理退卡"),

    /**
     * 距离交通卡开通未满 X
     * 天，暂无法办理退卡
     */
    ACTIVATION_TIME_NOT_MET(130810, "通卡返回开卡时间少于X天不允许退卡"),

    /**
     * 您的卡片余额小于 0
     * 元，暂无法办理退卡
     */
    BALANCE_IS_BELOW_ZERO(130823, "您的卡片余额小于0元，暂无法办理退卡"),

    /**
     * 您在 X 天内退款金额累
     * 计退款金额上限，暂无
     * 法退卡
     */
    REFUND_LIMIT_REACHED(130824, "您的卡片余额小于 0 元，暂无法办理退卡"),

    /**
     * 卡内余额超过 X 元不支
     * 持退卡，您可以刷卡消
     * 费至低于 X 元后重新发
     * 起退卡申请，如有疑问
     * 请咨询客服
     */
    CARD_BALANCE_EXCEEDS_REFUND_LIMIT(130825, "卡内余额超过 X 元不支 持退卡，您可以刷卡消 费至低于 X 元后重新发 起退卡申请，如有疑问 请咨询客服（check_return 校验未通过）"),

    /**
     * 在一个时间周期内，超
     * 过单个账户允许退卡退
     * 服务费的次数，暂无法
     * 办理退卡
     */
    DELETE_CARD_REACHED_LIMIT(130826, "在一个时间周期内，超 过单个账户允许退卡退 服务费的次数，暂无法 办理退卡"),

    /**
     * 文件大小超过限制
     */
    FILE_SIZE_VALID_ERROR(160005, "上传文件大小不能超过1m"),

    /**
     * 提交失败，该卡号已申请开卡费补贴，请勿重复提交。
     */
    CARD_NO_COUPON_PARTICIPATE(153401, "提交失败，该卡号已申请开卡费补贴，请勿重复提交。"),

    /**
     * 提交失败，您已申请该交通卡的开卡费补贴。
     */
    USER_COUPON_PARTICIPATE(153403, "提交失败，您已申请该交通卡的开卡费补贴。"),
    /**
     * 交失败，您已领取该交通卡的开卡费补贴。
     */
    USER_COUPON_RECEIVED(153404, "提交失败，您已领取该交通卡的开卡费补贴。"),
    /**
     * 用户申请领取优惠券 用户已参加此活动，不能再次参与
     */
    USER_COUPON_APPLY(153405, "用户已参加此活动，不能再次参与"),

    /**
     * 渠道不支持获取开票回执
     */
    NOT_SUPPORT_GET_INVOICEAUTHCODE(133301, "渠道不支持获取开票回执"),

    /**
     * 订单未支付，不能开发票
     */
    ORDER_NOT_PAY(133302, "订单未支付"),

    /**
     * 订单未完成，不能开发票
     */
    ORDER_NOT_COMPLETE(133304, "订单未完成，不能开票"),

    /**
     * 订单已退款
     */
    ORDER_REFUND(133305, "订单已退款"),

    /**
     * sp接口返回错误
     */
    GET_INVOICEAUTHCODE_ERROR(133306, "sp接口返回错误"),

    /**
     * 车钥匙删除未完成
     */
    NFC_CARKEY_DELETE_INCOMPLETE(134100, "车钥匙未删除完成，请先重试完成删除"),

    /**
     * 当前设备存在其他账号开通的此车钥匙
     */
    EXIST_ANOTHER_DK_FOR_THIS_DEVICE(130610, "当前设备存在其他账号开通的此车钥匙"),

    /**
     * 系统异常，请稍后重试（三方服务返回系统错误）
     */
    LOAN_SERVICE_ERROR(170001, "系统异常，请稍后重试"),

    /**
     * 短信验证码
     */
    VERIFY_SMSCODE_ERROR(170002, "验证码错误"),

    /**
     * 验证码连续输错超过限制次数，请2小时后再试
     */
    VERIFY_SMSCODE_CONTINUOUS_COUNT_ERROR(170009, "验证码连续输错超过限制次数，请2小时后再试"),

    /**
     * 用户未准入，请先准入（用于授信，借款，还款前置检查）
     */
    USER_NOT_ACCESS(170003, "用户未准入，请先准入"),

    /**
     * 用户已注销
     */
    USER_IS_LOGOUT(170004, "用户已注销"),

    /**
     * 无有效的用户信息
     */
    NO_VALID_USERINFO(170005, "无有效的用户信息"),

    /**
     * 用户不存在
     */
    USER_NOT_EXIST(170006, "用户不存在"),

    /**
     * 重复申请授信
     */
    DUPLICATE_CREATE_APPLY(170501, "用户已存在额度"),

    /**
     * 授信失败
     */
    CREATE_APPLY_FAIL(170502, "授信申请失败"),

    /**
     * 用户未实名
     */
    CREDIT_APPLY_USER_INFO_NOT_EXIST(170503, "用户未实名"),

    /**
     * 无效操作
     */
    INVALID_OPERATION(170007, "无效操作"),

    /**
     * 无效的订单号
     */
    INVALID_ORDER(170008, "无效的订单号"),

    /**
     * 身份证非法或与已实名的身份证不匹配
     */
    REAL_NAME_INVALID(170211, "10001005 身份证非法或与已实名的身份证不匹配"),

    /**
     * 实名信息已使用
     */
    THE_REALNAME_INFO_IS_USED(170221, "身份证已与度小满完成绑定，同一身份证在使用其他荣耀账号绑定"),

    /**
     * 准入失败，没有符合条件渠道
     */
    ACCESS_FAILED_NO_SUITABLE_CP(170223, "准入失败，没有符合条件渠道"),

    /**
     * 您已获取额度，请勿重复申请
     */
    ALREADY_CREDIT_SUCCESS_PLEASE_NOT_APPLY_AGAIN(170224,"您已获取额度，请勿重复申请"),

    /**
     * 您的申请在审核中，请勿重复申请
     */
    ALREADY_CREDIT_AUDITING_PLEASE_NOT_APPLY_AGAIN(170225,"您的申请在审核中，请勿重复申请"),

    /**
     * 用户当前额度状态异常请联系客服处理
     */
    THE_LIMIT_STATUS_ERROR_PLEASE_CONTACT_CUSTOMER_SERVICE(170226, "用户当前额度状态异常请联系客服处理"),

    /**
     * 您已绑定用户信息，请勿重复绑定
     */
    HAVING_BOUND_USER_INFO_PLEASE_NOT_DO_AGAIN(170227,"您已绑定用户信息，请勿重复绑定"),

    // --------------------------------------短信相关-------------------------
    /**
     * 发送失败，请确认信息后重试
     */
    SEND_MESSAGE_FAIL(173405, "发送失败，请确认信息后重试"),

    /**
     * 发送频繁，请1分钟后再发送
     */
    SEND_MESSAGE_FREQUENT(173406, "发送频繁，请1分钟后再发送"),

    /**
     * 验证频繁，请稍后重试
     */
    VERIFY_MESSAGE_FREQUENT(173407, "验证频繁，请稍后重试"),

    /**
     * 验证码过期
     */
    VERIFY_CODE_EXPIRE(173408, "验证码过期"),

    /**
     * 验证码错误
     */
    VERIFY_CODE_ERROR(173409, "验证码错误"),

    /**
     * 修改手机号场景，输入手机号和当前准入手机号一致
     */
    MODIFY_PHONE_ERROR(173411, "该手机号与当前绑定的手机号相同"),


    /**
     * 短信服务异常，发送失败
     */
    SNS_SEND_ERROR(173401, "短信服务异常，发送失败"),


    /**
     * 60s内重复发送
     */
    SNS_REPEAT_SEND_FOR_LOAN(173402, "60秒内请勿重复发送"),

    /**
     * 重复还款
     */
    REPAY_DUPLICATION(171908, "重复还款"),

    /**
     * 支付失败
     */
    PAY_FAIL(171909, "支付失败"),

    /**
     *余额不足，请使用其他支付方式
     */
    INSUFFICIENT_BALANCE(171910,"余额不足，请使用其他支付方式"),
    /**
     * 本期借据已结清，请勿重复还款
     */
    THE_LOAN_IS_SETTLED(171702, "本期借据已结清，请勿重复还款"),

    /**
     * 新锁期借据不可部分还当期
     */
    LOCK_LOAN_NO_PARTIAL_REPAYMENT_FOR_NOW(171901, "新锁期借据不可部分还当期"),

    /**
     * 新锁期借据不可部分还未来期
     */
    LOCK_LOAN_NO_PARTIAL_REPAYMENT_FOR_FEATURE(171902, "新锁期借据不可部分还未来期"),

    /**
     * 借据已结清，请勿重复还款
     */
    LOAN_SETTLED_PLEASE_NOT_REPAY(171701, "借据已结清，请勿重复还款"),

    /**
     * 验证码已发送，请勿频繁操作。
     */
    LOAN_SMS_SEND_FREQUEENT(173404, "验证码已发送，请勿频繁操作。"),
    /**
     * 当前借据处于禁还期
     */
    THE_LOAN_IS_BAN(171703, "当前借据处于禁还期"),

    /**
     * 新锁期借据部分，已被拦截
     */
    NEW_LOCK_UP_INTERCEPTED(171704, "新锁期借据部分，已被拦截"),

    LOCK_NOT_PART_REPAY_CUR(171704, "新锁期借据不可部分还当期"),

    LOCK_NOT_PART_REPAY_FEATURE(171705, "新锁期借据不可部分还未来期"),

    NOT_FOUND_RELATION_TRANSACTION(171706, "未查询到相关交易"),

    DISCOUNT_AMOUNT_NOT_MATCH_ACTUAL_AMOUNT(171707, "优惠金额与实际不符"),

    REPAY_AMOUNT_TOO_BIG(171708, "还款金额过大"),

    EXIST_NOT_FINISH_REPAY_ORDER(171709, "存在未结束的还款订单"),

    LOCK_PART_INTERCEPT(171710, "新锁期借据部分，已被拦截"),

    COUPON_NOT_COMPLY_RULE(171711, "优惠券不符合使用规则"),

    COUPON_USED_REPAY(171712, "优惠券已使用"),

    COUPON_EXPIRED(171213, "优惠券已过期"),

    COUPON_NOT_EXIST_REPAY(171214, "优惠券不存在"),


    /**
     * 未查询到相关交易
     */
    NO_TRANSACTION_FOUND(171903, "未查询到相关交易"),

    /**
     * 优惠金额与实际不符
     */
    DISCOUNT_MISMATCH(171904, "优惠金额与实际不符"),

    /**
     * 还款金额过大
     */
    HIGH_REPAYMENT_SUM(171905, "还款金额过大"),

    /**
     * 存在未结束的还款订单
     */
    UNSETTLED_REPAYMENT(171906, "存在未结束的还款订单"),

    /**
     * 新锁期借据部分，已被拦截
     */
    PARTIAL_INTERCEPT(171907, "新锁期借据部分，已被拦截"),

    /**
     * 超过1天短信发送限制次数，请明天再试
     */
    SNS_SEND_OUT_OF_LIMIT_FOR_LOAN(173403, "超过1天短信发送限制次数，请明天再试"),

    /**
     * 借款金额验证未通过，请稍后再试
     */
    LOAN_TRAIL_ERROR(170901, "借款金额验证未通过，请稍后再试"),

    /**
     * 可用额度不足
     */
    LOAN_CREDIT_NOT_ENOUGH(170902, "可用额度不足"),

    /**
     * 不支持该借款期数，请重新选择
     */
    LOAN_TERM_NOT_SUPPORT(170903, "不支持该借款期数，请重新选择"),

    /**
     *不支持该借款类型，请重新选择
     */
    LOAN_TYPE_NOT_SUPPORT(170904, "不支持该借款类型，请重新选择"),

    /**
     * 当前订单不支持该优惠券
     */
    CUR_ORDER_NOT_SUPPORT_COUPON(170905, "当前订单不支持该优惠券"),

    /**
     * 当前订单优惠券不支持该期数
     */
    CUR_COUPON_NOT_SUPPORT_TERM(170906, "当前订单优惠券不支持该期数"),

    /**
     * 当前订单优惠券活动未开始
     */
    CUR_ORDER_COUPON_NOT_START(170907, "当前订单优惠券活动未开始"),

    /**
     * 当前订单优惠券已过期
     */
    CUR_ORDER_COUPON_EXPIRE(170908, "当前订单优惠券已过期"),


    /**
     * 在度小满存在放款中订单（dxm返回411082）
     */
    EXIST_LOAN_IN_PROGRESS(171001, "存在放款中订单"),

    /**
     * 发起订单号重复（dxm返回102041）
     */
    DUPLICATE_ORDER_NUMBER(171002, "发起订单号重复"),

    /**
     * 四项信息加解密失败（手机号、姓名、身份证、银行卡）（dxm返回600000004）
     */
    ENCRYPT_DECRYPT_FAIL(171003, "四项信息加解密失败（手机号、姓名、身份证、银行卡）"),

    /**
     * 借款失败，您的申请评估未通过
     */
    LOAN_FAIL(171004, "借款失败，您的申请评估未通过"),

    /**
     *信息不完整，请重新上传"
     */
    LOAN_INFO_NOT_ENOUGH(171005, "信息不完整，请重新上传"),

    /**
     *证件审核不通过，请重新上传
     */
    LOAN_DOCUMENT_REVIEW_FAIL(171006, "证件审核不通过，请重新上传"),

    /**
     * 证件审核中，请稍后重试
     */
    LOAN_DOCUMENT_REVIEWING(171007, "证件审核中，请稍后重试"),

    /**
     * ⽤⼾流程状态异常
     */
    USER_PROCESS_STATUS_ABNORMAL_LOAN(171008, "用户流程状态异常"),

    /**
     * 人脸比对失败，请重新上传
     */
    FACE_CHECK_FAIL(171009, "人脸对比失败，请重新上传"),

    /**
     * 不支持当前期数，请更换后重试
     */
    LOAN_CUR_TERM_NOT_SUPPORT(171010, "不支持当前期数，请更换后重试"),

    /**
     * 贷款金额过小，请重新输入
     */
    LOAN_AMOUNT_TOO_SMALL(171011, "贷款金额过小，请重新输入"),

    /**
     * 银行卡校验失败，请重新绑卡
     */
    BANKCARD_CHECK_FAIL(171012, "银行卡校验失败，请重新绑卡"),

    /**
     * 订单已失效，请重新发起⼀笔新的订单
     */
    ORDER_EXPIRE(171013, "订单已失效，请重新发起⼀笔新的订单"),

    /**
     * 无效订单号
     */
    INVALID_ORDER_NUMBER(171014, "无效订单号"),

    /**
     * 用户不存在
     */
    USER_NOT_EXIST_1(200002, "用户不存在"),

    /**
     * 用户已注销
     */
    USER_CANCELLED(200003, "用户已注销"),



    /**
     * 未查询到绑定记录
     */
    USER_BIND_INFO_IS_NOT_EXIST(173801, "未查询到用户绑定记录，请重新绑定"),

    /**
     * 存在授信申请中订单，不能停服
     */
    EXIST_UNDER_REVIEW_CREDIT_APPLY(173702, "存在授信申请中订单，不能停服"),

    /**
     * 存在借款申请订单，不能停服
     */
    EXIST_UNDER_REVIEW_LOAN_APPLY(173703, "存在借款申请订单，不能停服"),

    /**
     * 存在还款中订单，不能停服
     */
    EXIST_UNDER_REPAYING_REPAY(173704, "存在还款中订单，不能停服"),

    /**
     * 存在未结清借款，不能注销
     */
    EXIST_UNSETTLED_LOAN(173701, "存在未结清借款，不能注销"),

    /**
     * 实名信息未找到
     */
    USER_REALNAME_INFO_NOT_FOUNT(173802, "未获取到实名信息，请重新实名"),

    /**
     * 查询数据范围参数长度错误
     */
    PARAM_RANGE_FLAG_LENGTH_INCORRECT(173803, "查询数据范围参数长度错误"),

    /**
     * 入参查询数据范围错误
     */
    PARAM_RANGE_FLAG_INCORRECT(173804, "入参查询数据范围错误"),

    /**
     * 用户流程状态异常（dxm返回102047）
     */
    USER_PROCESS_STATUS_ABNORMAL(102047, "用户流程状态异常"),

    /**
     * 订单已失效，请重新发起一笔新的订单（dxm返回102065）
     */
    ORDER_INVALID(102065, "订单已失效，请重新发起一笔新的订单"),

    /**
     * 未获取到实名信息，请重新实名
     */
    REAL_NAME_NOT_OBTAINED(170201, "未获取到实名信息，请重新实名"),

    /**
     * 调用支付接口出错，获取账单失败
     */
    IAP_GET_CHECK_ERROR(143001, "调用支付接口出错，获取账单失败"),

    /**
     * 调用积分接口出错，获取积分信息失败
     */
    OPENAPI_REQUEST_FAIL(142901, "调用积分接口出错，获取积分信息失败"),

    /**
     * 调用红包接口出错，获取红包余额失败
     */
    REDPACKET_REQUEST_FAIL(142801, "调用红包接口出错，获取红包余额失败"),

    /**
     * 不支持结清证明
     */
    SETTLEMENT_SEND_NOT_SUPPORT(172401, "荣耀借钱暂无法直接为您提供开具结清证明服务，如需开具结清证明，请咨询{0}客服{1}"),

    /**
     *  结清证明开具太频繁
     */
    SETTLEMENT_SEND_TOO_FREQUENT(12841071, "每30分钟只能申请一次，请稍后再试"),

    /**
     *  审批信息格式有误
     */
    APPROVAL_INFO_FORMAT_ERROR(180103, "审批信息格式有误"),

    /**
     * DMP返回结果异常
     */
    DMP_RESULT_EXCEPTION(180101, "DMP异常"),

    FLOWNO_NOTEXIST(180402,"查无此FlowNo"),

    APPROVAL_PROCESS_EXCEPTION(180401,"审批流程异常"),
    /**
     * MagicOper返回结果异常
     */
    MAGICOPER_RESULT_EXCEPTION(180201, "MagicOper返回结果异常"),

    /**
     * 已经有审批中的订单，不能重复提交申请
     */
    APPROVAL_SUBMIT_EXCEPTION(180104, "已经有审批中的电子流，不能重复提交申请"),

    INVALID_NUMBER_FORMAT(180106,"分流比例只能填写数字，并且小数点后最多两位"),

    PARSE_EXCEPTION(180106,"转换异常"),

    MODIFY_PHONE_FACE_CHECK_EXCEPTION(175511,"操作超时，请重试"),

    MODIFY_PHONE_CLIENT_EXCEPTION(175510,"手机号修改失败，请稍后重试"),

    /**
     * 分流审批，材料依据未上传
     */
    MATERIAL_EXCEPTION(180105, "分流审批，材料依据未上传"),

    LOAN_QUERY_LIMIT_EXCEEDED(190001, "超过单日查询次数限制"),

    LOAN_QUERY_USERID_ERROR(190002, "客户id错误");



    private final Integer code;

    private final String message;


    public static WalletResultCode getWalletResultCode(Integer code) {
        for (WalletResultCode value : WalletResultCode.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
