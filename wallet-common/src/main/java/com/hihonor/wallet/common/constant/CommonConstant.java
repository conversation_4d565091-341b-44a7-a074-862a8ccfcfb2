/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.wallet.common.constant;

import java.util.Arrays;
import java.util.List;

/**
 * 通用常量
 *
 * <AUTHOR>
 * @since 2022-06-01
 */
public interface CommonConstant {
    /**
     * 7.0.3.000
     */
    String VERSION_703 = "7.0.3.000";

    /**
     * http post
     */
    String POST = "post";

    /**
     * http get
     */
    String GET = "get";

    /**
     * access-token
     */
    String ACCESS_TOKEN = "access-token";

    /**
     * uid
     */
    String REQ_UID = "requid";

    /**
     * 手机号
     */
    String MOBILE_NO = "mobileNo";

    /**
     * loan 渠道id
     */
    String SUPPLIER = "supplier";

    /**
     * traceId
     */
    String MDC_TRACE = "traceId";

    /**
     * userId
     */
    String MDC_USERID = "userId";

    /**
     * deviceId
     */
    String MDC_DEVICEID = "deviceId";

    /**
     * 模型分主键deviceId，未脱敏
     */
    String MDC_MODEL_SCORE_DEVICEID = "modelScoreDeviceId";

    /**
     * 设备型号
     */
    String MDC_DEVICEMODEL = "deviceModel";

    /**
     * 设备型号
     */
    String MDC_VERSIONCODE = "versionCode";

    /**
     * 接口名称
     */
    String INTERFACE_NAME = "interfaceName";
    /**
     * 服务名称
     */
    String SERVICE_NAME = "serviceName";
    /**
     * 版本
     */
    String VERSION = "version";
    /**
     * type
     */
    String TYPE = "type";
    /**
     * sourceId
     */
    String SOURCE_ID = "sourceId";
    /**
     * destinationId
     */
    String DESTINATION_ID = "destinationId";
    /**
     * requestArgs
     */
    String REQUEST_ARGS = "requestArgs";
    /**
     * responseTime
     */
    String RESPONSE_TIME = "responseTime";

    /**
     * flag
     */
    String FLAG = "flag";
    /**
     * responseCode
     */
    String RESPONSE_CODE = "responseCode";
    /**
     * responseInfo
     */
    String RESPONSE_INFO = "responseInfo";
    /**
     * sourceResponseCode
     */
    String SOURCE_RESPONSE_CODE = "sourceResponseCode";
    /**
     * sourceResponseInfo
     */
    String SOURCE_RESPONSE_INFO = "sourceResponseInfo";
    /**
     * responseData
     */
    String RESPONSE_DATA = "responseData";
    /**
     * IS_SENSITIVE
     */
    String IS_SENSITIVE = "isSensitive";

    String TIME_KEY = "t";

    String BUSINESS_NAME_KEY = "p";

    String BUSINESS_NAME_VALUE = "Wallet";

    String SERVICE_NAME_KEY = "s";

    String COLLECT_RULE_ID_KEY = "id";
    /**
     * GRAY_ENV 灰度环境
     */
    String GRAY_ENV = "wallet-env";

    String OAID = "oaid";

    /**
     * GRAY_ENV_VALUE 灰度环境请求头赋值
     */
    String GRAY_ENV_VALUE = "GRAY_WALLET";

    /**
     * getDetailInfo接口查询范围标志
     */
    String QUERY_USER_INFO_RANGE_FLAG = "11111111000001";

    /**
     * getInfoByServiceAT接口查询范围标志
     * 第1位：是否查询注册时间
     * 第2位：是否查询用户状态
     * 第3位：是否查询用户deviceId
     */
    String QUERY_SERVICE_INFO_RANGE_FLAG = "100";

    /**
     * nonce的长度
     */
    int NONCE_LEN = 16;

    /**
     * deviceName
     */
    String DEVICE_NAME = "honor";

    /**
     * 端侧版本号规范
     * 0-299：开发测试包
     * 300-399：生产包
     */
    int VERSION_NUMBER = 300;

    /**
     * 端侧版本号规范
     * 0-299：开发测试包
     * 300-399：生产包
     */
    int VERSION_NUMBER_MAX = 399;

    /**
     * 端侧版本号
     */
    int NUMBER_1000 = 1000;

    /**
     * 卡包APP
     */
    String WALLET_PACKAGE_NAME = "com.hihonor.wallet";

    /**
     * 运动健康APP
     */
    String HEALTH_PACKAGE_NAME = "com.hihonor.health";

    /**
     * ios请求包名
     */
    String IOS_PACKAGE_NAME = "com.honor.health";

    /**
     * 手机U盾APP
     */
    String SHIELD_PACKAGE_NAME = "com.hihonor.hnpanpayservice";

    /**
     * 包名
     */
    String PACKAGE_NAME = "packageName";

    /**
     * 修改密码场景下的kafka消息sceneId
     */
    String KAFKA_MODIFY_SCENE_ID = "1";

    /**
     * 冻结账号场景下的kafka消息sceneId
     */
    String KAFKA_FREEZE_SCENE_ID = "2";

    /**
     * 变更账号场景下的kafka消息sceneId
     */
    String KAFKA_CHANGE_SCENE_ID = "3";

    /**
     * 销户场景下的kafka消息sceneId
     */
    String KAFKA_DELUSER_SCENE_ID = "4";

    /**
     * 删除设备（设备挤掉），用户删除单个设备场景下的kafka消息sceneId
     */
    String KAFKA_DELDEVICE_SCENE_ID = "5";

    /**
     * 客户端APK登出场景下的kafka消息sceneId（用户从当前手机客户端apk（设备）登出）
     */
    String KAFKA_SIGNOUT_SCENE_ID = "6";

    /**
     * 门禁卡对应的射频文件的aid, 默认为-1
     */
    String ACCESSCARD_RFCONFIG_CARD_AID = "-1";

    /**
     * 营销活动缓存key前缀
     */
    String MARKING_ACTIVITY_KEY_PREFIX = "marking_activity_card_type_";

    /**
     * 请求成功响应码
     */
    int RESP_OK = 200;

    /**
     * WALLET_VERSION_NAME
     */
    String WALLET_VERSION_NAME = "wallet_version_name";

    /**
     * 分和元的单位
     */
    int CENT_YUAN_UNIT = 100;

    int REFUNDDE_POSIT = 0;

    int DEFAULT_NUM = -1;

    int DEFAULT_SEINITSTATUS = 0;

    /**
     * 数据库字段
     */
    String CREATE_TIME = "createTime";

    String UPDATE_TIME = "updateTime";

    /**
     * 设备类型 1为手机
     */
    Integer DEVICE_TYPE_PHONE = 1;

    /**
     * 设备类型 2为手表
     */
    int DEVICE_TYPE_WATCH = 2;

    /**
     * 清除数据时，user_id设置为0
     */
    Long CLEAR_DATA_USER_ID = 0L;

    /**
     * 设备类型(honor) 0-手机，1-穿戴
     */
    int DEVICE_TYPE_HONOR_PHONE = 0;

    int DEVICE_TYPE_HONOR_WATCH = 1;

    int FROM_TIME_HOUR_NUMBER = -1;

    int TO_TIME_HOUR_NUMBER = 1;
    List<Integer> ANALOG_ACCESS_CARD = Arrays.asList(-1, 1);

    /**
     * 模拟门禁卡
     */
    int SIMULATED_ACCESS_CARD = 1;

    /**
     * 非加密门禁卡
     */
    int NOT_ENCRYPT_ACCESS_CARD = 1;

    /**
     * 创建空白卡
     */
    int BLANK_ACCESS_CARD = 9;

    /**
     * 加密门禁卡
     */
    int ENCRYPT_ACCESS_CARD = 2;

    /**
     * 模拟门禁卡
     */
    int ACCESS_BLANK_CARD_SIMULATED = 0;

    /**
     * 创建空白卡
     */
    int ACCESS_BLANK_CARD_BLACK = 1;

    /**
     * 默认分组卡片的cardType
     */
    int GROUP_CARD_TYPE = 0;

    /**
     * 没有cplc手机默认的cplc默认为"null"
     */
    String DEFAULT_CPLC_NULL = "null";

    String FILE_SUFFIX_JPEG = ".jpeg";

    String FILE_SUFFIX_JPG = ".jpg";

    String FILE_SUFFIX_PNG = ".png";

    String FILE_SUFFIX_GIF = ".gif";

    String FILE_SUFFIX_WEBP = ".webp";

    String CONTENT_TYPE_JPG = "image/jpg";

    String CONTENT_TYPE_PNG = "image/png";

    String CONTENT_TYPE_JPEG = "image/jpeg";

    String CONTENT_TYPE_GIF = "image/gif";

    String CONTENT_TYPE_WEBP = "image/webp";

    /**
     * 多语言销户卡片最小数量
     */
    int CANCEL_ACCOUNT_LANGUAGE_MIN_NUMBER = 0;

    /**
     * 多语言销户卡片最大数量
     */
    int CANCEL_ACCOUNT_LANGUAGE_MAX_NUMBER = 20;

    List SELECT_ARRAY_01 = Arrays.asList(0, 1);

    List SELECT_ARRAY_12 = Arrays.asList(1, 2);

    List SELECT_ARRAY_012 = Arrays.asList(0, 1, 2);

    String TRANSIT_CARD_CONFIG_ALL = "transitCardConfigAll";

    long MARKET_CACHE_KEY_OVERTIME = 10 * 60;

    long MARKET_LOOP_OVERTIME = 10 * 60 * 1000;

    long SNOWBALL_VERSIONCODE_OVERTIME = 5 * 60 * 1000;

    //低于该版本需要插入redis，用于后面判断版本发短信用
    long SNOWBALL_SNS_LESS_VERSIONCODE = ********;

    int SHIFT_ENABLED = 1;

    int SHIFT_DISABLED = 0;

    int LOAN_PUSH_ACTION = 66;

    ////huks////
    /**
     * huks
     */
    String HUKS = "huks";

    /**
     * publicKey
     */
    String PUBLIC_KEY = "publicKey";

    /**
     * 挑战值
     */
    String CHALLENGE = "challenge";

    /**
     * 密钥
     */
    String SECRET_KEY = "secretKey";

    public static class MagicOperType {

        /**
         * 查询
         */
        public static final String SEARCH = "15";
    }

    /**
     * 非nfc设备
     */
    String NOT_NFC = "0";

    /**
     * push消息actionType
     */
    public static class PushActionType {
        /**
         * 交通卡锁定
         */
        public static final int LOCK_CARD = 1;

        /**
         * 交通卡解锁
         */
        public static final int UNLOCK_CARD = 2;

        /**
         * 交通卡、车钥匙、，门禁卡远程删卡
         */
        public static final int REMOTE_DELETE_CARD = 3;

        /**
         * 交通卡远程迁卡
         */
        public static final int REMOTE_MOVE_OUT = 7;

        /**
         * nfc车钥匙删除
         */
        public static final int DELETE_CAR_KEY = 4;

        /**
         * 自动充值开通状态通知
         */
        public static final int AUTO_RECHARGE_NOTIFY = 5;

        /**
         * 交通卡充值
         */
        public static final int RECHARGE = 6;

        /**
         * 交通卡充值订单支付成功
         */
        public static final int RECHARGE_PAY_SUCCESS = 12;

        /**
         * 退卡退款成功
         */
        public static final int NOT_OPEN = 21;

        /**
         * 退卡退款失败
         */
        public static final int OPENED = 22;

        /**
         * 优惠券到期提醒
         */
        public static final int COUPON_EXPIRE_REMIND = 32;

        /**
         * 被扫风控校验
         */
        public static final Integer SCANNED_QR_RISK = 33;

        /**
         * 被扫支付成功通知
         */
        public static final Integer SCANNED_QR_TRANS_SUCCESS = 34;

        /**
         * 银联解绑通知
         */
        public static final Integer DEVICE_UNBIND = 35;
    }

    /**
     * 风控错误码
     */
    public static class RiskControlErrorCode {
        /**
         * 拒绝
         */
        public static final String REFUSE = "999";

        /**
         * 允许
         */
        public static final String APPROVE = "300";
    }

    /**
     * 风控businesstype
     */
    public static class RiskControlBusinessType {
        /**
         * 授信
         */
        public static final String CREDIT_APPLY = "pocket_loanCredit";

        /**
         * 用户注销
         */
        public static final String LOAN_CLOSE = "pocket_loanCloseAcct";

        /**
         * 交通卡
         */
        public static final String TRANSCARD_VERIFY = "pocket_transCardVerify";

        /**
         * 银行卡--银联授权时，用户修改手机号
         */
        public static final String BANKCARD_AUTH_VERIFY = "pocket_bcAuthVerify";

    }

    /**
     * 风控短信下发结果
     */
    public static class RiskControlSendResult {
        /**
         * 成功
         */
        public static final Integer SUCCESS = 1;

        /**
         * 失败
         */
        public static final Integer FAIL = 0;

    }

    public static class PushTokenStatus {
        /**
         * 成功
         */
        public static final Integer SAVE = 0;

        /**
         * 失败
         */
        public static final Integer UNBIND = 1;

    }

    /**
     * 跳转方式
     */
    public static class AdActivityLinkType {
        /**
         * deeplink
         */
        public static final Integer DEEPLINK = 1;

        /**
         * h5
         */
        public static final Integer H5 = 3;

    }
}
