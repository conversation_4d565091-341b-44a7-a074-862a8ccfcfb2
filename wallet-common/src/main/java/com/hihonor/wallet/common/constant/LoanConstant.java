/*
 * Copyright (c) Honor Device Co., Ltd. 2022-2022. All rights reserved.
 */

package com.hihonor.wallet.common.constant;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.hihonor.wallet.common.util.http.HttpConstant;

/**
 * 通用常量
 *
 * <AUTHOR>
 * @since 2022-06-01
 */
public interface LoanConstant {
    /**
     * dxm授信申请
     */
    String DXM_LIMIT_APPLY_URI = "limit.apply";

    /**
     * dxm借款申请
     */
    String DXM_LOAN_APPLY_URI = "loan.apply";

    /**
     * dxm身份证检查
     */
    String DXM_ID_CARD_CHECK = "idcard.check";

    /**
     * jd 授信申请
     */
    String JD_LIMIT_APPLY_URI = "credit.apply";

    /**
     * jd借款申请
     */
    String JD_LOAN_APPLY_URI = "loan.apply";

    /**
     * jd身份证检查
     */
    String JD_ID_CARD_CHECK = "idcard.check";

    List<String> CHECK_LIST = Collections.unmodifiableList(Arrays.asList(
            LoanConstant.DXM_ID_CARD_CHECK,
            LoanConstant.DXM_LIMIT_APPLY_URI,
            LoanConstant.DXM_LOAN_APPLY_URI,
            HttpConstant.PUSH_MSG,
            LoanConstant.JD_LIMIT_APPLY_URI,
            LoanConstant.JD_LOAN_APPLY_URI,
            LoanConstant.JD_ID_CARD_CHECK
    ));

    Map<String, String> CREDIT_VERIFY_MAP = new HashMap<String, String>() {{
        put("AGREEMENT_CREDIT", "授信协议");
        put("CREDIT_PERSONAL_INFO", "完善资料");
        put("FACE_CHECK", "活体识别");
    }};

    Map<String, String> SUPPLIER_MAP = new HashMap<String, String>() {{
        put("1", "度小满金融");
        put("5", "京东金条");
    }};
}
