/*
 * Copyright (c) Honor Terminal Co., Ltd. 2022-2022. All rights reserved.
 *
 */

package com.hihonor.wallet.common.constant;

/**
 * http header常量
 *
 * <AUTHOR>
 * @since 2022-06-01
 */
public interface HeaderConstant {
    /**
     * 链路id
     */
    String MDC_TRACE = "traceId";

    /**
     * 设备id
     */
    String UUID = "x-uuid";

    /**
     * AT
     */
    String AT = "access-token";

    /**
     *
     */
    String IGONRE_AT = "x-ignoreAuth";

    /**
     * excel请求头
     */
    String EXCEL_HEADER = "application/vnd.ms-excel";

    /**
     * 设备型号
     */
    String DEVICE_MODEL = "deviceModel";

    /**
     * Referer请求头(CSRF校验)
     */
    String REFERER_HEADER = "Referer";
    /**
     * csrfToken请求头(CSRF校验-加密)
     */
    String CSRF_TOKEN_HEADER1 = "csrfToken";

    /**
     * rmsCsrfToken请求头(CSRF校验-明文)
     */
    String CSRF_TOKEN_HEADER2 = "walletCode";

    /**
     * 校验参数签名(CSRF校验-明文)
     */
    String CSRF_TOKEN_HEADER3 = "vaildToken";

    /**
     * 时间错
     */
    String TIMESTAMP = "timeStamp";

    /**
     * 防重放
     */
    String NONCE = "nonce";

    /**
     * app包名
     */
    String PACKAGE_NAME = "packageName";

    /**
     * APP证书指纹
     */
    String APP_PACKAGE_FINGER = "appFingerprint";

    /**
     * sdk版本号
     */
    String VERSION_CODE = "versionCode";

    /**
     * 用户ID
     */
    String USERID = "userId";

    /**
     * 设备ID
     */
    String DEVICEID = "deviceId";

    /**
     * sessionId
     */
    String SESSIONID = "w-sessionId";

    /**
     * 签名
     */
    String SIGN = "x-sign";

    /**
     * 对响应体进行签名
     */
    String RSP_SIGN = "x-server-sign";

    //huks//
    String HUKS_SIGN = "x-security-sign";
    String HUKS_CHALLENGE = "huks-challenge";

    /**
     * x-line-test，x-line-test=1表示是路测包，
     * 路测包不校验版本号是否支持，支持所有交通卡非下线状态且card_rule规则存在非下线的交通卡；
     */
    String XLINETEST = "x-line-test";

    String ContentType = "Content-Type";

    String hnWalletEnable = "hnWalletEnable";

    String nfcDevice = "x-nfc-device";

    String CID = "cid";

    String LOC = "x-loc";

    String SUB_CID = "x-sub-cid";

    String WALLET_KEY = "x-wallet-key";

    String WALLET_TOKEN = "x-wallet-token";

    /**
     * oaid
     */
    String OAID = "x-oaid";

    String MOCK_FLAG = "mockFlag";
}
