nacos:
  addr: ${NACOS_ADDR}
  namespace: ${NACOS_NAMESPACE}
  username: ${NACOS_USER}
  password: ${NACOS_PWD}
  group: ${GRAY_ENV:DEV}_GROUP

spring:
  profiles:
    active: ${ACTIVE_ENV:release}
  cloud:
    nacos:
      discovery:
        server-addr: ${nacos.addr}
        namespace: ${nacos.namespace}
        username: ${nacos.username}
        password: ${nacos.password}
        group: ${nacos.group}
      config:
        server-addr: ${nacos.addr}
        namespace: ${nacos.namespace}
        username: ${nacos.username}
        password: ${nacos.password}
        file-extension: yaml
        #指定从哪个group加载配置文件，不配置时默认为：DEFAULT_GROUP。【统一规范，必须配置】
        group: ${nacos.group}
        shared-configs[0]:
          data-id: loan-common.yaml
          group: ${nacos.group}
          refresh: true
        shared-configs[1]:
          data-id: loan-env.yaml
          group: ${nacos.group}
          refresh: true

management:
  health:
    redis:
      enabled: false

## 部署区域编码，枚举值见下方
docker_region: ${TRUSS.DOCKER_REGION}

## 版本
####文件导入无用，固定1.0即可
docker_version: 1.0

# 应用信息
application:
  ## 取值IAM项目ID
  appId: ${TRUSS.APP_ID}
  # 应用（微服务）名称，自定义
  appName: ${spring.application.name}
  ## 部署单元
  subAppId: ${spring.application.name}

# API MALL认证信息
truss:
  apimall:
    project: ${application.appId}
    ## 企业ID，固定值
    enterprise: 99999999999999999999999999999999
    ## 生产为： https://yun.hihonor.com
    endpoint: ${TRUSS.END_POINT}
    ## IAM中API集成账号
    account: ${TRUSS.IAM_ACCOUNT}
    secret: ${TRUSS.IAM_SECRET}
#  sentinel:
#    project: ${application.appId}
#    transport:
#      dashboard: mse.beta.hihonor.com
#    eager: true


