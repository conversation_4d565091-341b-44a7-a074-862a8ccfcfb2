"use client";

import React from "react";
import { Tab, Tabs } from "@heroui/react";


export default function Page() {
  return (
    <>
      <Tabs isVertical={true} size={"lg"} className={"h-"}>
        <Tab key="yzm" className={"w-full"} title="验证码">
          <iframe
            src="https://hnid-test-drcn.cloud.hihonor.com/authcode"
            className={"w-full h-[60vh]"}
            title="验证码"
            allowFullScreen
          />
        </Tab>

        <Tab key="sfz" className={"w-full"} title="随机身份证号">
          <iframe
            src="https://www.lddgo.net/common/idgenerator"
            className={"w-full h-screen"}
            title="验证码"
            allowFullScreen
          />
        </Tab>
      </Tabs>
    </>
  );
}
