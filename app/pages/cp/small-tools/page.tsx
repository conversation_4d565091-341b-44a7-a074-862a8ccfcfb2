"use client";

import React from "react";
import { Tab, Tabs } from "@heroui/react";
import { useUserStore } from "@/store/user";
import { reportTrackEvent } from "@/actions/cp/eventTrackingAction";
import { EventTrackingNameTypeEnum } from "@/types/cp/eventTracking";


export default function Page() {
  const user = useUserStore((state) => state.currentUser);

  const tabChange = (key:React.Key) => {
    switch (key) {
      case "yzm":
        reportTrackEvent(user?.id || 0, user?.username || "", EventTrackingNameTypeEnum.SMALL_TOOLS_YZM);
        break;
      case "sfz":
        reportTrackEvent(user?.id || 0, user?.username || "", EventTrackingNameTypeEnum.SMALL_TOOLS_SFZ);
      default:
        break;
    }
  }

  return (
    <>
      <Tabs isVertical={true} size={"lg"} onSelectionChange={tabChange}>
        <Tab key="yzm" className={"w-full"} title="验证码">
          <iframe
            src="https://hnid-test-drcn.cloud.hihonor.com/authcode"
            className={"w-full h-[60vh]"}
            title="验证码"
            allowFullScreen
          />
        </Tab>

        <Tab key="sfz" className={"w-full"} title="身份证银行卡号生成器">
          <iframe
            src="https://www.tl.beer/randbankcard.html"
            className={"w-full h-screen"}
            title="身份证"
            allowFullScreen
          />
        </Tab>
      </Tabs>
    </>
  );
}
