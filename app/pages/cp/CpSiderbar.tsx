"use client";
import {
  Dropdown,
  DropdownItem,
  Dropdown<PERSON><PERSON>u,
  Dropdown<PERSON>rigger,
  ScrollShadow,
  User,
} from "@heroui/react";

import React from "react";

import useSceneStore from "@/store/scene";
import { useUserStore } from "@/store/user";
import SiderItems from "@/app/pages/cp/SiderItems";

export const items: SidebarItem[] = [
  {
    key: "home",
    href: "/pages/cp/loan-user/all",
    icon: "solar:home-2-linear",
    title: "所有准入用户",
  },
  // {
  //   key: "smallTools",
  //   href: "pages/cp/small-tools",
  //   icon: "solar:users-group-two-rounded-outline",
  //   title: "小工具",
  // },
  {
    key: "smallTools",
    href: "/pages/cp/small-tools",
    icon: "iconoir:tools",
    title: "小工具",
  },
];

export enum SidebarItemType {
  Nest = "nest",
}

export type SidebarItem = {
  key: string;
  title: string;
  icon?: string;
  href?: string;
  type?: SidebarItemType.Nest;
  startContent?: React.ReactNode;
  endContent?: React.ReactNode;
  items?: SidebarItem[];
  className?: string;
};
export default function CpSidebar() {
  useSceneStore((state) => state.setScenes);
  const logout = useUserStore((state) => state.logoutUser);
  const user = useUserStore((state) => state.currentUser);

  // 退出登录的处理
  const handleLogout = (): void => {
    logout();
    window.location.reload();
  };

  return (
    <div className="flex flex-col gap-2 items-center w-56 min-h-screen bg-gray-900 text-white">
      <Dropdown placement="bottom-start">
        <DropdownTrigger>
          <User
            as="button"
            className="rounded-none flex flex-row  justify-normal w-full bg-gray-700 py-2 transition-transform"
            name={user?.username}
          />
          
        </DropdownTrigger>
        <DropdownMenu aria-label="User Actions" variant="flat">
          <DropdownItem key="logout" onPress={handleLogout}>
            退出
          </DropdownItem>
        </DropdownMenu>
      </Dropdown>

      <ScrollShadow className="w-full h-full max-h-full">
        <SiderItems defaultSelectedKey={"home"} items={items} />
      </ScrollShadow>
    </div>
  );
}
