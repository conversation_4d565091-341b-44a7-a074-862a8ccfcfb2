"use client";
import { useRouter } from "next/navigation";
import React, { useEffect } from "react";
import {
  addToast,
  Button,
  Dropdown,
  DropdownItem,
  DropdownMenu,
  DropdownTrigger,
  Listbox,
  ListboxItem,
  ListboxSection,
  User,
} from "@heroui/react";
import { Icon } from "@iconify/react";

import useSceneStore from "@/store/scene";
import { useUserStore } from "@/store/user";
import { deleteSceneById, getScenesByUserId } from "@/actions/sceneActions";
import { delay } from "@/utils/timeUtils";

export const ListboxWrapper = ({ children }: { children: React.ReactNode }) => (
  <div className="w-full max-w-[260px] border-small px-1 py-2 rounded-small border-default-200 dark:border-default-100">
    {children}
  </div>
);

export default function SceneSidebar() {
  const router = useRouter();
  const scenes = useSceneStore((state) => state.scenes);
  const setScenes = useSceneStore((state) => state.setScenes);
  const logout = useUserStore((state) => state.logoutUser);
  const user = useUserStore((state) => state.currentUser);

  useEffect(() => {
    const userId = user?.id;

    if (!userId) {
      setScenes([]);

      return;
    }

    fetchScenes();
  }, []);

  const fetchScenes = async () => {
    await delay(100);

    try {
      const result = await getScenesByUserId(user?.id);

      if ("error" in result) {
        setScenes([]); // 清空数据
      } else {
        setScenes(result);
      }
    } catch (err) {
      console.error("Error calling getScenesByUserId action:", err);
      setScenes([]); // 清空数据
    }
  };

  // 退出登录的处理
  const handleLogout = (): void => {
    logout();
    window.location.reload();
  };

  function RecentSceneDropdown(props: { sceneId: number }) {
    function deleteScene() {
      deleteSceneById(props.sceneId).then((r) => {
        addToast({
          title: "删除成功",
          color: "success",
        });
        fetchScenes();
      });
    }

    return (
      <Dropdown>
        <DropdownTrigger>
          <Icon
            className="text-default-500 opacity-0 group-hover:opacity-100"
            icon="solar:menu-dots-bold"
            width={24}
          />
        </DropdownTrigger>
        <DropdownMenu
          aria-label="Dropdown menu with icons"
          className="py-2"
          variant="faded"
        >
          <DropdownItem
            key="delete"
            className="text-danger-500 data-[hover=true]:text-danger-500"
            color="danger"
            startContent={
              <Icon
                className="text-danger-500"
                height={20}
                icon="solar:trash-bin-minimalistic-linear"
                width={20}
              />
            }
            onPress={deleteScene}
          >
            删除
          </DropdownItem>
        </DropdownMenu>
      </Dropdown>
    );
  }

  return (
    <div className="flex flex-col gap-2 items-center w-56 min-h-screen bg-gray-900 text-white">
      <div className="flex items-center w-full bg-gray-700 py-2 px-2">
        <Dropdown placement="bottom-start">
          <DropdownTrigger>
            <User
              as="button"
              className="rounded-none flex flex-row justify-normal flex-1 bg-transparent transition-transform"
              name={user?.username}
            />
          </DropdownTrigger>
          <DropdownMenu aria-label="User Actions" variant="flat">
            <DropdownItem key="logout" onPress={handleLogout}>
              退出
            </DropdownItem>
          </DropdownMenu>
        </Dropdown>
        <Icon
          icon="solar:logout-2-linear"
          className="text-gray-300 hover:text-white cursor-pointer transition-colors"
          width={20}
          height={20}
          onClick={handleLogout}
        />
      </div>

      <div className="w-full flex flex-col items-center">
        <Button
          className="w-3/4 mb-4"
          color={"primary"}
          radius="full"
          onPress={() => router.push("/pages/scene/create")}
        >
          创建新的场景
        </Button>

        <Button
          className="w-3/4"
          color={"primary"}
          radius="full"
          onPress={() => router.push("/pages/scene/getAt")}
        >
          获取AT
        </Button>

        <ListboxWrapper>
          <Listbox aria-label="Listbox menu with sections" variant="flat">
            <ListboxSection
              classNames={{
                base: "py-0",
                heading: "py-0 pl-[10px] text-small text-default-400",
              }}
              title="最近场景"
            >
              {scenes.map((scene) => (
                <ListboxItem
                  key={scene.id}
                  className="h-[44px] px-[12px] py-[10px] text-default-500"
                  endContent={<RecentSceneDropdown sceneId={scene.id} />}
                  onPress={() => router.push(`/pages/scene/${scene.id}`)}
                >
                  {scene.name}
                </ListboxItem>
              ))}
            </ListboxSection>
          </Listbox>
        </ListboxWrapper>
      </div>
    </div>
  );
}
