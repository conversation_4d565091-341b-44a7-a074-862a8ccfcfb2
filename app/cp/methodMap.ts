import { userBind } from "@/app/cp/userBind";
import { contractQuery } from "@/app/cp/contractQuery";
import { creditApply } from "@/app/cp/creditApply";
import { creditInfoQuery } from "@/app/cp/creditInfoQuery";
import { creditAddUrlQuery } from "@/app/cp/addUrlQuery";
import { couponList } from "@/app/cp/couponList";
import { repayPlan } from "@/app/cp/repayPlan";
import { creditApplyStatus } from "@/app/cp/creditApplyStatus";
import { loanTrial } from "@/app/cp/loanTrial";
import { bankCardList } from "@/app/cp/bankCardList";
import { bankCardBind } from "@/app/cp/bankCardBind";
import { bankCardSmsVerify } from "@/app/cp/bankCardSmsVerify";

export const methodMap = {
  "user.bind": userBind,
  "contract.query": contractQuery,
  "credit.apply": creditApply,
  "credit.info.query": creditInfoQuery,
  "credit.addUrl.query": creditAddUrlQuery,
  "coupon.list": couponList,
  "repay.plan": repayPlan,
  "credit.apply.status": creditApplyStatus,
  "loan.trial": loanTrial,
  "bankcard.list": bankCardList,
  "bankcard.bind": bankCardBind,
  "bankcard.sms.verify": bankCardSmsVerify,
};

export type MethodName = keyof typeof methodMap;
