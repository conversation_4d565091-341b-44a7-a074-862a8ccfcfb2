import prisma from "@/utils/prisma";
import {encryptRes} from "@/utils/decrypt";
import { addInterfaceLog } from "@/actions/interfaceLogAction";
import { getLoanUserByUserId } from "@/actions/loanUserActions";

const interfaceName = "contract.query";

enum BusinessType {
  CREDIT = 1,
  BIND_CARD = 2,
  LOAN = 3,
}

export interface ParamType {
  userId: string;
  businessType: BusinessType;
}
export async function contractQuery(params:ParamType) {
  const user = await getLoanUserByUserId(params.userId);

  if (!user) {
    console.log("用户不存在");

    return encryptRes({
      code: 1,
      message: "用户不存在",
      data: null,
    });
  }
  const res = {
    code: 0,
    message: "success",
    data: {
      totalNum: 1,
      records:[
        {
          defaultChecked: 1,
          forceRead: 0,
          contractContent: "<h3>hello</h3>",
          contractUrl: "https://www.badiu.com",
          contractName: "度小满协议"
        }
      ]},
  };

  addInterfaceLog({
    userId: user.id,
    interfaceName,
    requestData: JSON.stringify(params),
    responseData: JSON.stringify(res),
  })
  return encryptRes(res);
}