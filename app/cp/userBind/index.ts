import {encryptRes} from "@/utils/decrypt";
import {CreditApplyStatusEnum} from "@/app/cp/creditApply/type";
import {createLoanUser, getLoanUserByUserId} from "@/actions/loanUserActions";
import {RepayMethod} from "@/app/cp/creditInfoQuery";
import {CardBandStatusEnum} from "@/types/cp/bankCard";

export interface ParamType {
  userId: string;
  realName: string;
  ctfCode: string;
  mobileNo: string;
}
export async function userBind(params: ParamType) {
  const user = await getLoanUserByUserId(params.userId);

  if (!user) {
    await createLoanUser({
      ...params,
      creditInfo: {
        status: {
          applyStatus: 0,
          productInfos: [
            {
              dayRate: "0.0005",
              apr: "18.25",
              repayMethod: RepayMethod.EqualPrincipalAndInterest,
              earlyRepay: true,
              termNums: [3, 6, 12],
            },
          ],
        },
        actions: {
          creditApplyStatus: CreditApplyStatusEnum.PASSED,
          creditLimit: 2000000,
        },
      },
      bankCardInfo: {
        status: {
          bankCardList: []
        },
        action: {
          bandStatus: CardBandStatusEnum.PASSED
        }
      },
      createTime: Date.now(),
    });
  }

  const res = {
    code: 0,
    message: "success",
    data: {
      // 准入状态，1-准入成功，0-准入失败
      access: 1,
      openId: params.userId,
    },
  };

  return encryptRes(res);
}
